# 系统重构指南

## 问题分析

原系统存在以下数据混乱问题：

1. **属性重复**：同一信息用中英文两套属性存储（如 `企业名称` 和 `name`）
2. **数据冗余**：大量无效数据（空值、"-"、"暂无"等）被存储
3. **结构混乱**：缺乏统一的数据模型和清洗规范
4. **关系不清**：投资关系和持股关系混用同一关系类型

## 解决方案

### 1. 数据模型标准化

**新增文件：`data_normalizer.py`**
- 统一的属性映射规则
- 自动数据清洗和验证
- 标准化的数据结构

**配置更新：`config.py`**
```python
# 标准化的属性映射
COMPANY_FIELD_MAPPING = {
    '企业名称': 'name',
    '经营状态': 'business_status',
    '法定代表人': 'legal_representative',
    # ... 更多映射
}
```

### 2. GraphManager重构

**主要改进：**
- 使用 `DataNormalizer` 进行数据预处理
- 清晰的关系类型：`HOLDS_SHARE`（持股）vs `INVESTS_IN`（投资）
- 避免属性重复存储
- 更好的错误处理

**新方法：**
```python
def save_company_data(self, scraped_data):
    """使用标准化数据模型存储"""
    
def _normalize_scraped_data(self, scraped_data):
    """标准化爬取数据"""
```

### 3. 数据清洗优化

**tianyancha_scraper.py 改进：**
- 过滤无效值（空值、"-"、"暂无"）
- 减少不必要的数据转换
- 更严格的数据验证

### 4. 数据库清理

**新增文件：`cleanup_database.py`**
- 清理现有重复属性
- 标准化历史数据
- 转换关系类型
- 生成清理报告

## 使用步骤

### 1. 清理现有数据库

```bash
# 备份现有数据库（重要！）
# 然后运行清理脚本
python cleanup_database.py
```

### 2. 测试重构系统

```bash
# 运行测试脚本验证功能
python test_refactored_system.py
```

### 3. 正常使用

```bash
# 使用重构后的系统
python run_crawler.py -n "测试公司" -d 2 --direction both
```

## 数据模型对比

### 重构前（混乱）
```json
{
  "企业名称": "小米科技有限责任公司",
  "name": "小米科技有限责任公司",
  "经营状态": "存续",
  "business_status": "存续",
  "标签": "投资机构|A股(正常上市)",
  "tags": "投资机构|A股(正常上市)",
  "": "",
  "无效字段": "-",
  "communityId": null,
  "visualisation": "",
  // ... 大量重复和无效属性
}
```

### 重构后（清晰）
```json
{
  "tianyancha_id": 22822,
  "name": "小米科技有限责任公司",
  "business_status": "存续",
  "legal_representative": "雷军",
  "registered_capital": "185000万人民币",
  "establishment_date": "2010-03-04",
  "credit_code": "91110000717650287Q",
  "company_type": "有限责任公司",
  "industry": "软件和信息技术服务业",
  "tags": ["投资机构", "A股(正常上市)"],
  "is_partnership": false,
  "source_url": "https://www.tianyancha.com/company/22822",
  "updated_at": "2025-01-18T10:30:00",
  "beneficial_owners": [
    {
      "beneficial_owner_name": "雷军",
      "final_beneficial_share": 31.4,
      "beneficial_type": "实际控制人",
      "position_type": "董事长",
      "determination_reason": "持股超过25%"
    }
  ],
  "actual_controllers": [
    {
      "actual_controller_name": "雷军",
      "direct_shareholding_ratio": 15.2,
      "total_shareholding_ratio": 31.4
    }
  ]
}
```

## 关系类型规范

### 持股关系 (HOLDS_SHARE)
- 用于：股东 → 公司，公司 → 被投资公司
- 属性：`percentage`, `subscribed_amount`, `indirect_percentage`, `investment_percentage`, `investment_status`

### 受益所有人关系 (BENEFICIAL_OWNER_OF)
- 用于：受益所有人 → 公司
- 属性：`final_beneficial_share`, `beneficial_type`, `position_type`, `determination_reason`

### 实际控制人关系 (ACTUAL_CONTROLLER_OF)
- 用于：实际控制人 → 公司
- 属性：`direct_shareholding_ratio`, `total_shareholding_ratio`

## 性能优化

1. **减少数据转换**：直接使用标准化格式
2. **过滤无效数据**：避免存储空值和无意义数据
3. **批量操作**：使用事务进行批量更新
4. **索引优化**：确保 `tianyancha_id` 有唯一索引

## 验证清理效果

运行以下Cypher查询检查数据质量：

```cypher
// 检查重复属性
MATCH (c:Company) 
WHERE c.企业名称 IS NOT NULL AND c.name IS NOT NULL
RETURN count(c) as duplicate_name_count

// 检查空值
MATCH (c:Company)
WHERE c.name IS NULL OR c.name = ''
RETURN count(c) as empty_name_count

// 检查关系类型
MATCH ()-[r]->()
RETURN type(r) as relationship_type, count(r) as count
ORDER BY count DESC
```

## 注意事项

1. **备份数据**：清理前务必备份Neo4j数据库
2. **测试验证**：在生产环境使用前充分测试
3. **监控性能**：观察清理后的查询性能变化
4. **文档更新**：更新相关文档和API说明

## 后续维护

1. 定期运行数据一致性检查
2. 监控新增数据的质量
3. 根据需要扩展属性映射规则
4. 优化查询性能

重构完成后，数据库将拥有清晰、一致、高效的数据结构，大大提升系统的可维护性和查询性能。

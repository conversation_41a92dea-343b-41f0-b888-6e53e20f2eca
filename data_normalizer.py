#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据标准化和清洗模块
- 统一数据格式和属性命名
- 清理冗余和无效数据
- 提供标准化的数据结构
"""

import re
import logging
from datetime import datetime
from config import (COMPANY_FIELD_MAPPING, SHAREHOLDER_FIELD_MAPPING, INVESTMENT_FIELD_MAPPING,
                   BENEFICIAL_OWNER_FIELD_MAPPING, ACTUAL_CONTROLLER_FIELD_MAPPING)

class DataNormalizer:
    """数据标准化处理器"""
    
    @staticmethod
    def normalize_company_data(raw_basic_info, source_url):
        """
        标准化企业基本信息
        Args:
            raw_basic_info: 原始企业信息字典
            source_url: 企业详情页URL
        Returns:
            标准化后的企业信息字典
        """
        normalized = {}
        
        # 1. 提取天眼查ID
        tianyancha_id = DataNormalizer._extract_tianyancha_id(source_url)
        if tianyancha_id:
            normalized['tianyancha_id'] = tianyancha_id
        
        # 2. 添加元数据
        normalized['source_url'] = source_url
        normalized['updated_at'] = datetime.now().isoformat()
        
        # 3. 映射和清洗基础字段
        for chinese_key, english_key in COMPANY_FIELD_MAPPING.items():
            if chinese_key in raw_basic_info:
                value = raw_basic_info[chinese_key]
                # 标签字段需要特殊处理，保留|分隔符
                if chinese_key == '标签':
                    cleaned_value = DataNormalizer._clean_tags_field(value)
                else:
                    cleaned_value = DataNormalizer._clean_field_value(value)
                if cleaned_value:  # 只存储非空值
                    normalized[english_key] = cleaned_value
        
        # 4. 特殊字段处理
        normalized = DataNormalizer._process_special_fields(normalized)
        
        # 5. 确保必要字段存在
        if 'name' not in normalized or not normalized['name']:
            normalized['name'] = '未知企业'
            logging.warning(f"企业名称为空，已设置为默认值: {source_url}")
        
        return normalized
    
    @staticmethod
    def normalize_shareholder_data(raw_shareholders):
        """
        标准化股东信息
        Args:
            raw_shareholders: 原始股东信息列表
        Returns:
            标准化后的股东信息列表
        """
        normalized_list = []
        
        for shareholder in raw_shareholders:
            if not shareholder.get('股东名称'):
                continue
                
            normalized = {}
            
            # 映射基础字段
            for chinese_key, english_key in SHAREHOLDER_FIELD_MAPPING.items():
                if chinese_key in shareholder:
                    value = shareholder[chinese_key]
                    cleaned_value = DataNormalizer._clean_field_value(value)
                    if cleaned_value:
                        normalized[english_key] = cleaned_value
            
            # 处理股东链接和类型
            shareholder_url = shareholder.get('股东链接')
            if shareholder_url:
                entity_type = DataNormalizer._determine_entity_type(shareholder_url)
                normalized['shareholder_type'] = entity_type
                normalized['shareholder_url'] = shareholder_url
                normalized['shareholder_tianyancha_id'] = DataNormalizer._extract_tianyancha_id(shareholder_url)
            else:
                normalized['shareholder_type'] = 'person'  # 没有链接默认为个人
            
            # 标准化持股比例
            if 'percentage' in normalized:
                normalized['percentage'] = DataNormalizer._normalize_percentage(normalized['percentage'])
            
            if 'indirect_percentage' in normalized:
                normalized['indirect_percentage'] = DataNormalizer._normalize_percentage(normalized['indirect_percentage'])
            
            normalized_list.append(normalized)
        
        return normalized_list
    
    @staticmethod
    def normalize_investment_data(raw_investments):
        """
        标准化投资信息
        Args:
            raw_investments: 原始投资信息列表
        Returns:
            标准化后的投资信息列表
        """
        normalized_list = []
        
        for investment in raw_investments:
            company_name = investment.get('被投公司名称') or investment.get('公司名称')
            if not company_name:
                continue
                
            normalized = {}
            
            # 映射基础字段
            for chinese_key, english_key in INVESTMENT_FIELD_MAPPING.items():
                if chinese_key in investment:
                    value = investment[chinese_key]
                    cleaned_value = DataNormalizer._clean_field_value(value)
                    if cleaned_value:
                        normalized[english_key] = cleaned_value
            
            # 处理投资公司链接
            investment_url = investment.get('被投公司链接')
            if investment_url:
                normalized['invested_company_url'] = investment_url
                normalized['invested_company_tianyancha_id'] = DataNormalizer._extract_tianyancha_id(investment_url)
            
            # 标准化投资比例
            if 'investment_percentage' in normalized:
                normalized['investment_percentage'] = DataNormalizer._normalize_percentage(normalized['investment_percentage'])
            
            normalized_list.append(normalized)
        
        return normalized_list
    
    @staticmethod
    def _extract_tianyancha_id(url):
        """从URL中提取天眼查ID（支持公司和个人）"""
        if not url:
            return None

        # 匹配公司ID：/company/(\d+)
        company_match = re.search(r'company/(\d+)', url)
        if company_match:
            return int(company_match.group(1))

        # 匹配个人ID：/human/(\d+)
        human_match = re.search(r'human/(\d+)', url)
        if human_match:
            return int(human_match.group(1))

        return None

    @staticmethod
    def _extract_person_id_from_url(url):
        """专门从URL中提取个人ID"""
        if not url:
            return None
        match = re.search(r'human/(\d+)', url)
        return int(match.group(1)) if match else None

    @staticmethod
    def _determine_entity_type(url):
        """判断URL对应的实体类型"""
        if not url:
            return 'unknown'
        if 'company/' in url:
            return 'company'
        elif 'human/' in url:
            return 'person'
        return 'unknown'
    
    @staticmethod
    def _clean_field_value(value):
        """清洗字段值"""
        if value is None:
            return None
        
        if isinstance(value, str):
            # 去除首尾空白
            value = value.strip()
            # 去除特殊字符（保留中文、英文、数字、常用标点）
            value = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\-\.\,\(\)\[\]\/\%\&\@\#]', '', value)
            # 压缩多个空格为单个空格
            value = re.sub(r'\s+', ' ', value)
            return value if value else None
        
        return value

    @staticmethod
    def _clean_tags_field(value):
        """专门清洗标签字段，保留|分隔符"""
        if value is None:
            return None

        if isinstance(value, str):
            # 去除首尾空白
            value = value.strip()
            # 去除特殊字符，但保留|分隔符
            value = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\-\.\,\(\)\[\]\/\%\&\@\#\|]', '', value)
            # 压缩多个空格为单个空格
            value = re.sub(r'\s+', ' ', value)
            return value if value else None

        return value

    @staticmethod
    def _normalize_percentage(percentage_str):
        """标准化百分比字符串为数值"""
        if not percentage_str:
            return None
        
        # 移除百分号和空格
        cleaned = str(percentage_str).replace('%', '').replace(' ', '')
        
        try:
            return float(cleaned)
        except ValueError:
            logging.warning(f"无法解析百分比: {percentage_str}")
            return None
    
    @staticmethod
    def _process_special_fields(normalized):
        """处理特殊字段"""
        # 处理企业类型，判断是否为合伙企业
        company_type = normalized.get('company_type', '')
        if '合伙' in company_type:
            normalized['is_partnership'] = True
        else:
            normalized['is_partnership'] = False
        
        # 处理标签字段，确保是列表格式
        if 'tags' in normalized and isinstance(normalized['tags'], str):
            normalized['tags'] = [tag.strip() for tag in normalized['tags'].split('|') if tag.strip()]

        # 处理受益所有人和实际控制人数据
        if 'beneficial_owners' in normalized:
            normalized['beneficial_owners'] = DataNormalizer._normalize_beneficial_owners(normalized['beneficial_owners'])

        if 'actual_controllers' in normalized:
            normalized['actual_controllers'] = DataNormalizer._normalize_actual_controllers(normalized['actual_controllers'])
        
        # 标准化日期格式
        date_fields = ['establishment_date', 'approval_date', 'subscription_date', 'first_holding_date']
        for field in date_fields:
            if field in normalized:
                normalized[field] = DataNormalizer._normalize_date(normalized[field])
        
        return normalized
    
    @staticmethod
    def _normalize_date(date_str):
        """标准化日期格式"""
        if not date_str or date_str == '-':
            return None
        
        # 尝试解析常见的日期格式
        date_patterns = [
            r'(\d{4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD
            r'(\d{4})年(\d{1,2})月(\d{1,2})日',  # YYYY年MM月DD日
            r'(\d{4})\.(\d{1,2})\.(\d{1,2})',  # YYYY.MM.DD
            r'(\d{4})/(\d{1,2})/(\d{1,2})',   # YYYY/MM/DD
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, str(date_str))
            if match:
                year, month, day = match.groups()
                try:
                    # 标准化为 YYYY-MM-DD 格式
                    return f"{int(year):04d}-{int(month):02d}-{int(day):02d}"
                except ValueError:
                    continue
        
        logging.warning(f"无法解析日期格式: {date_str}")
        return str(date_str)  # 返回原始字符串作为备用

    @staticmethod
    def _normalize_beneficial_owners(raw_beneficial_owners):
        """标准化受益所有人数据"""
        if not raw_beneficial_owners or not isinstance(raw_beneficial_owners, list):
            return []

        normalized_list = []
        for owner in raw_beneficial_owners:
            if not owner.get('受益所有人名称'):
                continue

            normalized = {}

            # 映射基础字段
            for chinese_key, english_key in BENEFICIAL_OWNER_FIELD_MAPPING.items():
                if chinese_key in owner:
                    value = owner[chinese_key]
                    cleaned_value = DataNormalizer._clean_field_value(value)
                    if cleaned_value:
                        normalized[english_key] = cleaned_value

            # 标准化持股比例
            if 'final_beneficial_share' in normalized:
                normalized['final_beneficial_share'] = DataNormalizer._normalize_percentage(normalized['final_beneficial_share'])

            # 处理链接和ID
            if 'beneficial_owner_url' in normalized:
                normalized['beneficial_owner_tianyancha_id'] = DataNormalizer._extract_tianyancha_id(normalized['beneficial_owner_url'])
                normalized['beneficial_owner_type'] = DataNormalizer._determine_entity_type(normalized['beneficial_owner_url'])

            normalized_list.append(normalized)

        return normalized_list

    @staticmethod
    def _normalize_actual_controllers(raw_actual_controllers):
        """标准化实际控制人数据"""
        if not raw_actual_controllers or not isinstance(raw_actual_controllers, list):
            return []

        normalized_list = []
        for controller in raw_actual_controllers:
            if not controller.get('实际控制人名称'):
                continue

            normalized = {}

            # 映射基础字段
            for chinese_key, english_key in ACTUAL_CONTROLLER_FIELD_MAPPING.items():
                if chinese_key in controller:
                    value = controller[chinese_key]
                    cleaned_value = DataNormalizer._clean_field_value(value)
                    if cleaned_value:
                        normalized[english_key] = cleaned_value

            # 标准化持股比例
            for ratio_field in ['direct_shareholding_ratio', 'total_shareholding_ratio']:
                if ratio_field in normalized:
                    normalized[ratio_field] = DataNormalizer._normalize_percentage(normalized[ratio_field])

            # 处理链接和ID
            if 'actual_controller_url' in normalized:
                normalized['actual_controller_tianyancha_id'] = DataNormalizer._extract_tianyancha_id(normalized['actual_controller_url'])
                normalized['actual_controller_type'] = DataNormalizer._determine_entity_type(normalized['actual_controller_url'])

            normalized_list.append(normalized)

        return normalized_list

# 属性清理工具使用指南

## 概述

为了减少Neo4j数据库中的键值数量，提升查询性能，我们提供了两个属性清理工具：

1. **`cleanup_rare_properties.py`** - 功能完整的清理工具
2. **`quick_cleanup_properties.py`** - 简化的快速清理工具

## 工具对比

| 功能 | cleanup_rare_properties.py | quick_cleanup_properties.py |
|------|---------------------------|----------------------------|
| 支持的节点类型 | 所有节点类型 | 仅Company节点 |
| 预览功能 | ✅ | ❌ |
| 试运行模式 | ✅ | ❌ |
| 详细报告 | ✅ | 基础统计 |
| 自定义阈值 | ✅ | 固定阈值10 |
| 命令行参数 | 丰富 | 简单 |

## 使用方法

### 1. 快速清理工具（推荐新手使用）

```bash
# 显示属性统计
python quick_cleanup_properties.py stats

# 执行清理（阈值=10）
python quick_cleanup_properties.py

# 显示帮助
python quick_cleanup_properties.py help
```

### 2. 完整清理工具（推荐高级用户）

```bash
# 生成详细的属性使用报告
python cleanup_rare_properties.py --report-only

# 预览将要清理的属性（不实际删除）
python cleanup_rare_properties.py --preview -t 10

# 试运行清理（显示操作但不执行）
python cleanup_rare_properties.py --dry-run -t 10

# 实际执行清理（阈值=10）
python cleanup_rare_properties.py -t 10

# 仅清理Company节点的稀有属性
python cleanup_rare_properties.py -t 10 -l Company

# 使用更严格的阈值（阈值=5）
python cleanup_rare_properties.py -t 5
```

## 参数说明

### cleanup_rare_properties.py 参数

- `-t, --threshold`: 使用次数阈值（默认10）
- `-l, --label`: 指定节点标签（如Company、Person）
- `--dry-run`: 试运行模式，不实际删除
- `--report-only`: 仅生成报告
- `--preview`: 预览将要删除的属性

## 受保护的属性

以下核心属性不会被删除，无论使用次数多少：

```python
protected_keys = [
    'tianyancha_id',        # 天眼查ID（主键）
    'name',                 # 名称
    'created_at',           # 创建时间
    'updated_at',           # 更新时间
    'cleaned_at',           # 清理时间
    'business_status',      # 经营状态
    'legal_representative', # 法定代表人
    'registered_capital',   # 注册资本
    'establishment_date',   # 成立日期
    'credit_code',          # 统一社会信用代码
    'source_url',           # 来源URL
    'is_partnership',       # 是否合伙企业
    'company_type',         # 企业类型
    'industry',             # 行业
    'business_scope',       # 经营范围
    'tags'                  # 标签
]
```

## 使用场景

### 场景1：首次清理

```bash
# 1. 先查看当前状况
python cleanup_rare_properties.py --report-only

# 2. 预览清理效果
python cleanup_rare_properties.py --preview -t 10

# 3. 执行清理
python cleanup_rare_properties.py -t 10
```

### 场景2：定期维护

```bash
# 使用快速工具进行定期清理
python quick_cleanup_properties.py
```

### 场景3：深度清理

```bash
# 使用更严格的阈值
python cleanup_rare_properties.py -t 5

# 或者只清理特定类型的节点
python cleanup_rare_properties.py -t 10 -l Company
```

## 清理效果示例

### 清理前
```
总属性数量: 156
总属性记录数: 45,230

使用频率分布:
- 仅使用1次: 45 个属性
- 使用2-5次: 23 个属性
- 使用6-10次: 12 个属性
- 使用11-50次: 18 个属性
- 使用51-100次: 8 个属性
- 使用100+次: 50 个属性
```

### 清理后（阈值=10）
```
总属性数量: 76
总属性记录数: 44,850

清理统计:
- 删除了 80 个稀有属性
- 清理了 380 个属性记录
- 数据库大小减少约 15%
- 查询性能提升约 20%
```

## 注意事项

1. **备份数据**：清理前务必备份Neo4j数据库
2. **测试环境**：建议先在测试环境验证清理效果
3. **业务影响**：确认删除的属性不会影响业务逻辑
4. **分批清理**：对于大型数据库，建议分批清理

## 安全措施

1. **受保护属性**：核心业务属性不会被删除
2. **确认机制**：实际删除前需要用户确认
3. **详细日志**：记录所有清理操作
4. **试运行模式**：可以预览清理效果

## 性能优化建议

1. **定期清理**：建议每月执行一次清理
2. **合适阈值**：根据数据规模调整阈值
   - 小型数据库（<10万节点）：阈值=5
   - 中型数据库（10-100万节点）：阈值=10
   - 大型数据库（>100万节点）：阈值=20

3. **监控效果**：清理后监控查询性能变化

## 故障排除

### 问题1：连接数据库失败
```bash
# 检查config.py中的数据库配置
# 确保Neo4j服务正在运行
```

### 问题2：清理过程中断
```bash
# 重新运行清理脚本
# 脚本会自动跳过已清理的属性
```

### 问题3：误删重要属性
```bash
# 从备份恢复数据库
# 调整受保护属性列表
```

## 最佳实践

1. **清理前分析**：使用`--report-only`了解数据分布
2. **渐进式清理**：从高阈值开始，逐步降低
3. **定期维护**：建立定期清理计划
4. **监控指标**：跟踪数据库大小和查询性能
5. **文档记录**：记录每次清理的结果和影响

通过合理使用这些工具，可以有效减少数据库中的冗余属性，提升系统性能。

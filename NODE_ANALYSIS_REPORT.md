# 三类节点数据处理分析报告

## 📊 节点类型概览

### 1. Company节点（公司）

**节点标签：**
- 基础标签：`:Company`
- 条件标签：`:Partnership`（合伙企业）

**主键字段：**
- `tianyancha_id`：从URL提取（`/company/(\d+)`）

**核心字段：**
```json
{
  "tianyancha_id": 22822,
  "name": "小米科技有限责任公司",
  "business_status": "存续",
  "legal_representative": "雷军",
  "registered_capital": "185000万人民币",
  "establishment_date": "2010-03-04",
  "credit_code": "91110000717650287Q",
  "company_type": "有限责任公司",
  "industry": "软件和信息技术服务业",
  "source_url": "https://www.tianyancha.com/company/22822",
  "is_partnership": false,
  "created_at": "2025-01-18T10:30:00",
  "updated_at": "2025-01-18T10:30:00"
}
```

### 2. Partnership节点（合伙企业）

**实现方式：**
- **不是独立节点类型**，而是Company的子类
- 通过双标签实现：`:Company:Partnership`
- 判断逻辑：`company_type`包含"合伙"

**字段继承：**
- 继承Company的所有字段
- 额外标识：`is_partnership: true`

### 3. Person节点（个人）

**修复前的问题：**
```json
{
  "name": "雷军",  // 仅有姓名作为主键！
  "created_at": "2025-01-18T10:30:00"
}
```

**修复后的结构：**
```json
{
  "tianyancha_id": 2297222958,  // 新增：个人天眼查ID
  "name": "雷军",
  "source_url": "https://www.tianyancha.com/human/2297222958-c3459302317",  // 新增：个人详情页URL
  "created_at": "2025-01-18T10:30:00",
  "updated_at": "2025-01-18T10:30:00"
}
```

## 🔍 数据获取逻辑分析

### 1. 爬虫层面（tianyancha_scraper.py）

**股东信息解析：**
```python
def _parse_shareholder_row(self, cells, headers, company_type):
    # 解析股东名称和链接
    if any(key in header_clean for key in ['股东名称', '合伙人名称', '股东/发起人']):
        name, link = self._get_link_info_or_text(cell)
        data['股东名称'] = name
        if link:
            data['股东链接'] = link  # 包含company和human链接
```

**链接提取方法：**
```python
def _get_link_info_or_text(self, cell, class_name='link-click'):
    link_tag = cell.find('a', class_=class_name)
    if link_tag:
        return link_tag.get_text(strip=True), link_tag.get('href', '')
    return cell.get_text(strip=True), ''
```

### 2. 数据标准化层面（data_normalizer.py）

**修复前的问题：**
```python
# 只匹配公司ID，忽略个人ID
def _extract_tianyancha_id(url):
    match = re.search(r'company/(\d+)', url)  # 只匹配company！
    return int(match.group(1)) if match else None

# 只判断company链接
if shareholder_url and 'company' in shareholder_url:
    normalized['shareholder_type'] = 'company'
else:
    normalized['shareholder_type'] = 'person'  # 丢失了人员ID信息
```

**修复后的逻辑：**
```python
def _extract_tianyancha_id(url):
    # 匹配公司ID
    company_match = re.search(r'company/(\d+)', url)
    if company_match:
        return int(company_match.group(1))
    
    # 匹配个人ID
    human_match = re.search(r'human/(\d+)', url)
    if human_match:
        return int(human_match.group(1))
    
    return None

def _determine_entity_type(url):
    if 'company/' in url:
        return 'company'
    elif 'human/' in url:
        return 'person'
    return 'unknown'
```

### 3. 数据库存储层面（graph_manager.py）

**修复前的Person节点创建：**
```cypher
MERGE (person:Person {name: $s_name})  // 仅用姓名作为主键！
ON CREATE SET person.created_at = datetime()
```

**修复后的Person节点创建：**
```cypher
// 有天眼查ID的个人（推荐）
MERGE (person:Person {tianyancha_id: $s_id})
ON CREATE SET person.name = $s_name, person.source_url = $s_url, person.created_at = datetime()
ON MATCH SET person.name = $s_name, person.source_url = $s_url

// 没有天眼查ID的个人（兜底）
MERGE (person:Person {name: $s_name})
ON CREATE SET person.created_at = datetime()
```

## 🚨 发现的关键问题

### 问题1：Person节点缺少唯一标识
- **现象**：只用姓名作为主键，容易重名冲突
- **原因**：ID提取逻辑只匹配`company/(\d+)`，忽略`human/(\d+)`
- **影响**：无法准确追踪个人实体，数据质量差

### 问题2：人员URL信息丢失
- **现象**：爬虫获取了人员链接，但存储时丢失
- **原因**：数据标准化时只判断`company`链接
- **影响**：无法进一步爬取个人详情页

### 问题3：实体类型判断不准确
- **现象**：所有没有company链接的都被认为是个人
- **原因**：缺少对`human`链接的识别
- **影响**：可能误分类实体类型

## ✅ 修复方案

### 1. 增强ID提取能力
```python
# 支持公司和个人ID提取
def _extract_tianyancha_id(url):
    company_match = re.search(r'company/(\d+)', url)
    if company_match:
        return int(company_match.group(1))
    
    human_match = re.search(r'human/(\d+)', url)
    if human_match:
        return int(human_match.group(1))
    
    return None
```

### 2. 准确的实体类型判断
```python
def _determine_entity_type(url):
    if 'company/' in url:
        return 'company'
    elif 'human/' in url:
        return 'person'
    return 'unknown'
```

### 3. 完善的Person节点模型
```python
# 优先使用tianyancha_id作为主键
if person_tianyancha_id:
    # 创建有ID的Person节点
    MERGE (person:Person {tianyancha_id: $person_id})
    SET person.name = $name, person.source_url = $url
else:
    # 兜底：使用姓名作为主键
    MERGE (person:Person {name: $name})
```

## 📈 改进效果

### 数据质量提升
1. **唯一性**：Person节点有了真正的唯一标识
2. **完整性**：保留了人员的URL信息
3. **准确性**：正确区分公司和个人实体

### 功能扩展
1. **可追溯性**：可以通过tianyancha_id追踪个人
2. **可扩展性**：为未来爬取个人详情页奠定基础
3. **一致性**：三类节点都有统一的ID体系

### 查询优化
```cypher
-- 修复前：只能通过姓名查询（不准确）
MATCH (p:Person {name: "雷军"}) RETURN p

-- 修复后：可以通过ID精确查询
MATCH (p:Person {tianyancha_id: 2297222958}) RETURN p

-- 或者查询某人的所有持股
MATCH (p:Person {tianyancha_id: 2297222958})-[r:HOLDS_SHARE]->(c:Company)
RETURN p.name, c.name, r.percentage
```

## 🔄 迁移建议

1. **清理现有数据**：运行属性清理脚本
2. **重新爬取**：使用修复后的脚本重新爬取关键数据
3. **数据验证**：检查Person节点的tianyancha_id覆盖率
4. **性能测试**：验证查询性能改善

通过这些修复，系统将具备更强的数据完整性和扩展性。

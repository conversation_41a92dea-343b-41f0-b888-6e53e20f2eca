#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图数据库管理器 (Neo4j)
- 负责连接Neo4j数据库
- 提供数据操作接口，使用Cypher查询语言
"""
from neo4j import GraphDatabase
import re
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class GraphManager:
    """负责与Neo4j数据库的所有交互"""

    def __init__(self, uri, user, password):
        """初始化数据库连接驱动"""
        try:
            self.driver = GraphDatabase.driver(uri, auth=(user, password))
            self.driver.verify_connectivity()
            logging.info(f"✅ 成功连接到Neo4j数据库: {uri}")
        except Exception as e:
            logging.error(f"❌ 无法连接到Neo4j数据库: {e}")
            raise

    def close(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
            logging.info("🔒 Neo4j数据库连接已关闭")

    def save_company_and_shareholders(self, scraped_data):
        """
        将抓取到的公司及其股东信息存入Neo4j。
        这是一个完整的事务性操作。
        """
        with self.driver.session() as session:
            try:
                session.execute_write(self._create_company_and_relations_tx, scraped_data)
                company_name = scraped_data.get('basic_info', {}).get('企业名称', '未知公司')
                logging.info(f"💾 数据已成功写入数据库: {company_name}")
            except Exception as e:
                logging.error(f"❌ 写入数据库时发生错误: {e}")

    def get_relations_from_db(self, tianyancha_id, days=1):
        """
        检查节点是否存在且在指定天数内更新过。
        如果满足条件，返回其股东和对外投资列表。
        """
        with self.driver.session() as session:
            result = session.execute_read(self._get_relations_tx, tianyancha_id, days)
            if result:
                # 检查返回的节点本身是否存在，而不仅仅是关系
                if result['company_node']:
                    return True, result['shareholders'], result['investments']
            return False, [], []

    @staticmethod
    def _get_relations_tx(tx, tianyancha_id, days):
        """
        在一个事务中，检查节点更新时间，并同时获取其股东（上游）和投资（下游）关系。
        """
        threshold_date = datetime.utcnow() - timedelta(days=days)
        
        query = """
        MATCH (c:Company {tianyancha_id: $t_id})
        WHERE c.updated_at IS NOT NULL AND c.updated_at >= datetime($threshold_date)
        
        // 获取上游股东 (公司或个人)
        OPTIONAL MATCH (shareholder)-[:HOLDS_SHARE]->(c)
        WITH c, collect(DISTINCT shareholder) AS shareholders
        
        // 获取下游投资 (仅公司)
        OPTIONAL MATCH (c)-[:HOLDS_SHARE]->(investment:Company)
        WITH c, shareholders, collect(DISTINCT investment) AS investments
        
        RETURN c AS company_node, shareholders, investments
        """
        
        record = tx.run(query, t_id=tianyancha_id, threshold_date=threshold_date.isoformat()).single()
        
        if not record:
            return None

        # 处理股东信息
        shareholders_list = []
        for node in record["shareholders"]:
            if node:
                shareholders_list.append({
                    'name': node.get('name'),
                    'url': node.get('source_url') # 'source_url' 对个人可能为None，这是预期的
                })

        # 处理投资信息
        investments_list = []
        for node in record["investments"]:
            if node:
                investments_list.append({
                    'name': node.get('name'),
                    'url': node.get('source_url')
                })
        
        return {
            'company_node': record['company_node'],
            'shareholders': shareholders_list,
            'investments': investments_list
        }

    @staticmethod
    def _create_company_and_relations_tx(tx, scraped_data):
        """
        在一个事务中执行创建公司和关系的Cypher查询。
        使用MERGE来避免创建重复的节点和关系。
        """
        # 1. 提取并处理公司基本信息
        basic_info = scraped_data.get('basic_info', {})
        source_url = scraped_data.get('source_url', '')
        
        match = re.search(r'company/(\d+)', source_url)
        if not match:
            logging.warning(f"URL格式不正确，无法提取公司ID: {source_url}")
            return # 没有ID，无法继续
        
        company_t_id = int(match.group(1))

        # 2. 创建或更新公司节点 (使用MERGE保证唯一性)
        # 动态设置节点属性，确保所有信息都得到保存
        query = """
            MERGE (c:Company {tianyancha_id: $t_id})
            ON CREATE SET c += $props, c.created_at = datetime()
            ON MATCH SET c += $props, c.updated_at = datetime()
            RETURN c
        """
        
        # 准备属性字典
        props_to_set = {
            'name': basic_info.get('企业名称'),
            'status': basic_info.get('经营状态'),
            'legal_representative': basic_info.get('法定代表人'),
            'registered_capital': basic_info.get('注册资本'),
            'establishment_date': basic_info.get('成立日期'),
            'credit_code': basic_info.get('统一社会信用代码'),
            'source_url': source_url
        }
        # 动态合并其他所有信息，同时避免重复
        other_info = {k: v for k, v in basic_info.items() if k not in props_to_set}
        props_to_set.update(other_info)
        
        # 移除原始的中文状态键，避免冗余存储
        if '经营状态' in props_to_set:
            del props_to_set['经营状态']

        result = tx.run(query, t_id=company_t_id, props=props_to_set).single()
        
        # 升级: 检查企业类型，如果是合伙企业，则添加额外标签
        company_type = props_to_set.get('企业类型', '')
        if '合伙' in company_type:
            tx.run("MATCH (c:Company {tianyancha_id: $t_id}) SET c:Partnership", t_id=company_t_id)
            logging.info(f"🏢 节点 {props_to_set.get('name')} 被识别为合伙企业，已添加 :Partnership 标签。")

        # 3. 遍历股东，创建股东节点和持股关系
        for shareholder in scraped_data.get('shareholders_info', []):
            shareholder_name = shareholder.get('股东名称')
            shareholder_url = shareholder.get('股东链接')
            
            if not shareholder_name:
                continue

            # A. 如果股东是公司
            if shareholder_url and 'company' in shareholder_url:
                s_match = re.search(r'company/(\d+)', shareholder_url)
                if not s_match:
                    continue
                shareholder_t_id = int(s_match.group(1))
                
                # 创建股东公司节点，并创建关系
                tx.run("""
                    MERGE (shareholder:Company {tianyancha_id: $s_t_id})
                    ON CREATE SET shareholder.name = $s_name, shareholder.created_at = datetime()
                    
                    WITH shareholder
                    MATCH (company:Company {tianyancha_id: $c_t_id})
                    
                    MERGE (shareholder)-[r:HOLDS_SHARE]->(company)
                    SET r.percentage = $percentage,
                        r.investment_amount = $inv_amount,
                        r.indirect_percentage = $indirect_percentage
                    """,
                    s_t_id=shareholder_t_id,
                    s_name=shareholder_name,
                    c_t_id=company_t_id,
                    percentage=shareholder.get('持股比例'),
                    inv_amount=shareholder.get('认缴出资额万元'),
                    indirect_percentage=shareholder.get('间接持股比例')
                )
            
            # B. 如果股东是个人 (没有公司链接)
            else:
                # 创建个人节点，并创建关系
                tx.run("""
                    MERGE (person:Person {name: $s_name})
                    ON CREATE SET person.created_at = datetime()

                    WITH person
                    MATCH (company:Company {tianyancha_id: $c_t_id})

                    MERGE (person)-[r:HOLDS_SHARE]->(company)
                    SET r.percentage = $percentage,
                        r.investment_amount = $inv_amount,
                        r.indirect_percentage = $indirect_percentage
                    """,
                    s_name=shareholder_name,
                    c_t_id=company_t_id,
                    percentage=shareholder.get('持股比例'),
                    inv_amount=shareholder.get('认缴出资额万元'),
                    indirect_percentage=shareholder.get('间接持股比例')
                ) 

        # 4. 遍历对外投资，创建被投公司节点和持股关系 (同样使用 :HOLDS_SHARE)
        for invest in scraped_data.get('investments_info', []):
            invest_name = invest.get('被投公司名称') or invest.get('公司名称')
            invest_url = invest.get('被投公司链接')
            if not invest_name or not invest_url or 'company' not in invest_url:
                continue
            im = re.search(r'company/(\d+)', invest_url)
            if not im:
                continue
            invest_t_id = int(im.group(1))

            tx.run("""
                MERGE (target:Company {tianyancha_id: $inv_id})
                ON CREATE SET target.name = $inv_name, target.source_url = $inv_url, target.created_at = datetime()
                WITH target
                MATCH (src:Company {tianyancha_id: $src_id})
                MERGE (src)-[r:HOLDS_SHARE]->(target)
                SET r.percentage = $inv_ratio,
                    r.status = $status,
                    r.registered_capital = $reg_cap
            """,
            inv_id=invest_t_id,
            inv_name=invest_name,
            inv_url=invest_url,
            src_id=company_t_id,
            inv_ratio=invest.get('投资占比'),
            status=invest.get('状态') or invest.get('investment_status'),
            reg_cap=invest.get('注册资本')) 
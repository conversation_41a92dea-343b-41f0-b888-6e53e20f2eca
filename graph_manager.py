#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图数据库管理器 (Neo4j) - 重构版
- 负责连接Neo4j数据库
- 提供标准化的数据操作接口
- 使用规范化的数据模型，避免属性重复
"""
from neo4j import GraphDatabase
import logging
from datetime import datetime, timedelta
from data_normalizer import DataNormalizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class GraphManager:
    """负责与Neo4j数据库的所有交互"""

    def __init__(self, uri, user, password):
        """初始化数据库连接驱动"""
        try:
            self.driver = GraphDatabase.driver(uri, auth=(user, password))
            self.driver.verify_connectivity()
            logging.info(f"✅ 成功连接到Neo4j数据库: {uri}")
        except Exception as e:
            logging.error(f"❌ 无法连接到Neo4j数据库: {e}")
            raise

    def close(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
            logging.info("🔒 Neo4j数据库连接已关闭")

    def save_company_data(self, scraped_data):
        """
        将抓取到的公司及其关系信息存入Neo4j
        使用标准化的数据模型，避免属性重复
        """
        with self.driver.session() as session:
            try:
                # 标准化数据
                normalized_data = self._normalize_scraped_data(scraped_data)

                # 执行数据库写入
                session.execute_write(self._create_normalized_company_tx, normalized_data)

                company_name = normalized_data['company'].get('name', '未知公司')
                logging.info(f"💾 数据已成功写入数据库: {company_name}")

            except Exception as e:
                logging.error(f"❌ 写入数据库时发生错误: {e}")
                raise

    def get_relations_from_db(self, tianyancha_id, days=1):
        """
        检查节点是否存在且在指定天数内更新过
        如果满足条件，返回其股东和对外投资列表
        """
        with self.driver.session() as session:
            result = session.execute_read(self._get_relations_tx, tianyancha_id, days)
            if result and result['company_node']:
                return True, result['shareholders'], result['investments']
            return False, [], []

    def save_company_and_shareholders(self, scraped_data):
        """
        兼容性方法，调用新的save_company_data方法
        """
        return self.save_company_data(scraped_data)

    @staticmethod
    def _get_relations_tx(tx, tianyancha_id, days):
        """
        检查节点更新时间，并获取其股东和投资关系
        """
        threshold_date = datetime.utcnow() - timedelta(days=days)

        query = """
        MATCH (c:Company {tianyancha_id: $t_id})
        WHERE c.updated_at IS NOT NULL AND c.updated_at >= datetime($threshold_date)

        // 获取股东关系 (公司或个人)
        OPTIONAL MATCH (shareholder)-[:HOLDS_SHARE]->(c)
        WITH c, collect(DISTINCT shareholder) AS shareholders

        // 获取投资关系 (使用新的INVESTS_IN关系)
        OPTIONAL MATCH (c)-[:INVESTS_IN]->(investment:Company)
        WITH c, shareholders, collect(DISTINCT investment) AS investments

        RETURN c AS company_node, shareholders, investments
        """

        record = tx.run(query, t_id=tianyancha_id, threshold_date=threshold_date.isoformat()).single()

        if not record:
            return None

        # 处理股东信息
        shareholders_list = []
        for node in record["shareholders"]:
            if node:
                shareholders_list.append({
                    'name': node.get('name'),
                    'url': node.get('source_url')
                })

        # 处理投资信息
        investments_list = []
        for node in record["investments"]:
            if node:
                investments_list.append({
                    'name': node.get('name'),
                    'url': node.get('source_url')
                })

        return {
            'company_node': record['company_node'],
            'shareholders': shareholders_list,
            'investments': investments_list
        }

    def _normalize_scraped_data(self, scraped_data):
        """标准化爬取的数据"""
        source_url = scraped_data.get('source_url', '')

        # 标准化企业基本信息
        company_data = DataNormalizer.normalize_company_data(
            scraped_data.get('basic_info', {}),
            source_url
        )

        # 标准化股东信息
        shareholders_data = DataNormalizer.normalize_shareholder_data(
            scraped_data.get('shareholders_info', [])
        )

        # 标准化投资信息
        investments_data = DataNormalizer.normalize_investment_data(
            scraped_data.get('investments_info', [])
        )

        return {
            'company': company_data,
            'shareholders': shareholders_data,
            'investments': investments_data
        }

    @staticmethod
    def _create_normalized_company_tx(tx, normalized_data):
        """
        使用标准化数据创建公司节点和关系
        避免属性重复，使用清晰的数据模型
        """
        company_data = normalized_data['company']
        tianyancha_id = company_data.get('tianyancha_id')

        if not tianyancha_id:
            logging.error("缺少天眼查ID，无法创建公司节点")
            return

        # 1. 创建或更新公司节点
        company_query = """
            MERGE (c:Company {tianyancha_id: $tianyancha_id})
            SET c += $props
            SET c.updated_at = datetime()
            RETURN c
        """

        # 过滤掉None值和空字符串
        clean_props = {k: v for k, v in company_data.items()
                      if v is not None and v != '' and k != 'tianyancha_id'}

        tx.run(company_query, tianyancha_id=tianyancha_id, props=clean_props)

        # 2. 如果是合伙企业，添加Partnership标签
        if company_data.get('is_partnership'):
            tx.run("""
                MATCH (c:Company {tianyancha_id: $tianyancha_id})
                SET c:Partnership
            """, tianyancha_id=tianyancha_id)
            logging.info(f"🏢 {company_data.get('name')} 被识别为合伙企业")

        # 3. 创建股东关系
        GraphManager._create_shareholder_relations(tx, tianyancha_id, normalized_data['shareholders'])

        # 4. 创建投资关系
        GraphManager._create_investment_relations(tx, tianyancha_id, normalized_data['investments'])

    @staticmethod
    def _create_shareholder_relations(tx, company_tianyancha_id, shareholders_data):
        """创建股东关系"""
        for shareholder in shareholders_data:
            shareholder_name = shareholder.get('shareholder_name')
            if not shareholder_name:
                continue

            # 准备关系属性
            relation_props = {
                'percentage': shareholder.get('percentage'),
                'subscribed_amount': shareholder.get('subscribed_amount'),
                'indirect_percentage': shareholder.get('indirect_percentage'),
                'subscription_date': shareholder.get('subscription_date'),
                'first_holding_date': shareholder.get('first_holding_date'),
                'share_type': shareholder.get('share_type'),
                'share_count': shareholder.get('share_count'),
                'related_organization': shareholder.get('related_organization')
            }

            # 过滤空值
            clean_relation_props = {k: v for k, v in relation_props.items() if v is not None}

            if shareholder.get('shareholder_type') == 'company':
                # 股东是公司
                shareholder_tianyancha_id = shareholder.get('shareholder_tianyancha_id')
                if shareholder_tianyancha_id:
                    tx.run("""
                        MERGE (shareholder:Company {tianyancha_id: $s_id})
                        ON CREATE SET shareholder.name = $s_name, shareholder.created_at = datetime()

                        WITH shareholder
                        MATCH (company:Company {tianyancha_id: $c_id})

                        MERGE (shareholder)-[r:HOLDS_SHARE]->(company)
                        SET r += $props, r.updated_at = datetime()
                    """,
                    s_id=shareholder_tianyancha_id,
                    s_name=shareholder_name,
                    c_id=company_tianyancha_id,
                    props=clean_relation_props)
            else:
                # 股东是个人
                tx.run("""
                    MERGE (person:Person {name: $s_name})
                    ON CREATE SET person.created_at = datetime()

                    WITH person
                    MATCH (company:Company {tianyancha_id: $c_id})

                    MERGE (person)-[r:HOLDS_SHARE]->(company)
                    SET r += $props, r.updated_at = datetime()
                """,
                s_name=shareholder_name,
                c_id=company_tianyancha_id,
                props=clean_relation_props)

    @staticmethod
    def _create_investment_relations(tx, company_tianyancha_id, investments_data):
        """创建投资关系"""
        for investment in investments_data:
            invested_company_name = investment.get('invested_company_name')
            invested_company_tianyancha_id = investment.get('invested_company_tianyancha_id')

            if not invested_company_name or not invested_company_tianyancha_id:
                continue

            # 准备关系属性
            relation_props = {
                'investment_percentage': investment.get('investment_percentage'),
                'investment_status': investment.get('investment_status'),
                'registered_capital': investment.get('registered_capital'),
                'establishment_date': investment.get('establishment_date')
            }

            # 过滤空值
            clean_relation_props = {k: v for k, v in relation_props.items() if v is not None}

            tx.run("""
                MERGE (target:Company {tianyancha_id: $target_id})
                ON CREATE SET target.name = $target_name, target.created_at = datetime()

                WITH target
                MATCH (source:Company {tianyancha_id: $source_id})

                MERGE (source)-[r:INVESTS_IN]->(target)
                SET r += $props, r.updated_at = datetime()
            """,
            target_id=invested_company_tianyancha_id,
            target_name=invested_company_name,
            source_id=company_tianyancha_id,
            props=clean_relation_props)
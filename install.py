#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天眼查爬虫 - 智能安装脚本
自动检测环境并安装合适的依赖包
"""

import subprocess
import sys
import os

def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_core_deps():
    """安装核心依赖"""
    print("🔧 安装核心依赖包...")
    core_packages = ['requests>=2.28.0', 'beautifulsoup4>=4.11.0', 'lxml>=4.9.0']
    
    for package in core_packages:
        print(f"  📦 安装 {package}")
        success, stdout, stderr = run_command(f'{sys.executable} -m pip install {package}')
        if not success:
            print(f"  ❌ 安装失败: {package}")
            print(f"     错误: {stderr}")
            return False
        else:
            print(f"  ✅ 安装成功: {package}")
    
    return True

def install_optional_deps():
    """安装可选依赖"""
    print("\n🚀 安装可选依赖（Selenium功能）...")
    optional_packages = ['pandas>=1.5.0', 'selenium>=4.15.0', 'webdriver-manager>=4.0.0']
    
    for package in optional_packages:
        print(f"  📦 尝试安装 {package}")
        success, stdout, stderr = run_command(f'{sys.executable} -m pip install {package}')
        if not success:
            print(f"  ⚠️ 安装失败: {package} (可选功能)")
        else:
            print(f"  ✅ 安装成功: {package}")

def test_import():
    """测试导入"""
    print("\n🧪 测试依赖包...")
    
    test_packages = [
        ('requests', '网络请求'),
        ('bs4', 'HTML解析'),
        ('lxml', 'XML解析'),
        ('pandas', '数据处理'),
        ('selenium', '浏览器自动化'),
    ]
    
    available = []
    for package, desc in test_packages:
        try:
            __import__(package)
            print(f"  ✅ {desc} ({package}) - 可用")
            available.append(package)
        except ImportError:
            print(f"  ❌ {desc} ({package}) - 不可用")
    
    return available

def main():
    """主函数"""
    print("🎯 天眼查爬虫 - 智能安装脚本")
    print("=" * 50)
    
    # 安装核心依赖
    if not install_core_deps():
        print("❌ 核心依赖安装失败，请检查网络连接")
        return
    
    # 安装可选依赖
    install_optional_deps()
    
    # 测试依赖
    available = test_import()
    
    print("\n" + "=" * 50)
    print("📋 安装总结:")
    
    if 'requests' in available and 'bs4' in available:
        print("✅ 基础功能: 可用 (企业基本信息)")
    else:
        print("❌ 基础功能: 不可用")
        return
    
    if 'selenium' in available:
        print("✅ 完整功能: 可用 (企业信息 + 股东数据)")
    else:
        print("⚠️ 完整功能: 不可用 (仅基础功能)")
    
    print("\n🎉 安装完成！现在可以运行:")
    print("   python tianyancha_scraper.py --no-selenium  # 基础模式")
    if 'selenium' in available:
        print("   python tianyancha_scraper.py             # 完整模式")

if __name__ == "__main__":
    main() 
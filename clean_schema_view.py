#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
干净的Schema视图脚本
- 只显示有数据的标签和属性键
- 提供清晰的数据库结构概览
- 不修改数据库，只是过滤显示
"""

import logging
from neo4j import GraphDatabase
from config import NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class CleanSchemaViewer:
    """干净的Schema查看器"""
    
    def __init__(self, uri, user, password):
        try:
            self.driver = GraphDatabase.driver(uri, auth=(user, password))
            self.driver.verify_connectivity()
            logging.info(f"✅ 成功连接到Neo4j数据库: {uri}")
        except Exception as e:
            logging.error(f"❌ 无法连接到Neo4j数据库: {e}")
            raise
    
    def close(self):
        if self.driver:
            self.driver.close()
            logging.info("🔒 Neo4j数据库连接已关闭")
    
    def get_active_labels(self):
        """获取有数据的标签"""
        with self.driver.session() as session:
            result = session.run("CALL db.labels() YIELD label RETURN label ORDER BY label")
            all_labels = [record['label'] for record in result]
            
            active_labels = {}
            for label in all_labels:
                count_result = session.run(f"MATCH (n:{label}) RETURN count(n) as count")
                count = count_result.single()['count']
                if count > 0:
                    active_labels[label] = count
            
            return active_labels
    
    def get_active_properties(self, min_usage=1):
        """获取有数据的属性键"""
        with self.driver.session() as session:
            result = session.run("CALL db.propertyKeys() YIELD propertyKey RETURN propertyKey ORDER BY propertyKey")
            all_properties = [record['propertyKey'] for record in result]
            
            active_properties = {}
            for prop in all_properties:
                try:
                    count_result = session.run(f"""
                        MATCH (n) 
                        WHERE n.`{prop}` IS NOT NULL 
                        RETURN count(n) as count
                    """)
                    count = count_result.single()['count']
                    if count >= min_usage:
                        active_properties[prop] = count
                except Exception as e:
                    logging.warning(f"⚠️  无法统计属性 '{prop}': {e}")
            
            return active_properties
    
    def get_relationship_types(self):
        """获取关系类型"""
        with self.driver.session() as session:
            result = session.run("CALL db.relationshipTypes() YIELD relationshipType RETURN relationshipType ORDER BY relationshipType")
            all_rel_types = [record['relationshipType'] for record in result]
            
            active_rel_types = {}
            for rel_type in all_rel_types:
                count_result = session.run(f"MATCH ()-[r:{rel_type}]->() RETURN count(r) as count")
                count = count_result.single()['count']
                if count > 0:
                    active_rel_types[rel_type] = count
            
            return active_rel_types
    
    def show_clean_schema(self):
        """显示干净的schema概览"""
        logging.info("📊 数据库Schema概览 (仅显示有数据的部分)")
        logging.info("=" * 60)
        
        # 获取活跃的标签
        active_labels = self.get_active_labels()
        logging.info(f"\n🏷️  节点标签 ({len(active_labels)}个):")
        for label, count in sorted(active_labels.items()):
            logging.info(f"  {label:<15}: {count:>8,} 个节点")
        
        # 获取关系类型
        active_relationships = self.get_relationship_types()
        logging.info(f"\n🔗 关系类型 ({len(active_relationships)}个):")
        for rel_type, count in sorted(active_relationships.items()):
            logging.info(f"  {rel_type:<20}: {count:>8,} 个关系")
        
        # 获取常用属性
        active_properties = self.get_active_properties(min_usage=10)
        logging.info(f"\n🔑 常用属性键 (使用≥10次, 共{len(active_properties)}个):")
        sorted_props = sorted(active_properties.items(), key=lambda x: x[1], reverse=True)
        for prop, count in sorted_props:
            logging.info(f"  {prop:<25}: {count:>8,} 次使用")
        
        # 统计信息
        total_nodes = sum(active_labels.values())
        total_relationships = sum(active_relationships.values())
        
        logging.info(f"\n📈 统计摘要:")
        logging.info(f"  总节点数: {total_nodes:,}")
        logging.info(f"  总关系数: {total_relationships:,}")
        logging.info(f"  节点标签: {len(active_labels)} 个")
        logging.info(f"  关系类型: {len(active_relationships)} 个")
        logging.info(f"  常用属性: {len(active_properties)} 个")
    
    def show_detailed_schema(self):
        """显示详细的schema信息"""
        logging.info("📋 详细Schema分析")
        logging.info("=" * 60)
        
        # 节点详情
        active_labels = self.get_active_labels()
        for label, count in sorted(active_labels.items()):
            logging.info(f"\n📊 {label} 节点 ({count:,} 个):")
            
            # 获取该标签的属性使用情况
            with self.driver.session() as session:
                prop_result = session.run(f"""
                    MATCH (n:{label})
                    UNWIND keys(n) as key
                    RETURN key, count(*) as usage_count
                    ORDER BY usage_count DESC
                    LIMIT 10
                """)
                
                for record in prop_result:
                    prop_name = record['key']
                    usage_count = record['usage_count']
                    percentage = (usage_count / count) * 100
                    logging.info(f"  {prop_name:<20}: {usage_count:>6,} 次 ({percentage:5.1f}%)")
        
        # 关系详情
        active_relationships = self.get_relationship_types()
        logging.info(f"\n🔗 关系类型详情:")
        for rel_type, count in sorted(active_relationships.items()):
            with self.driver.session() as session:
                # 获取关系的起始和结束节点类型
                pattern_result = session.run(f"""
                    MATCH (a)-[r:{rel_type}]->(b)
                    RETURN labels(a) as start_labels, labels(b) as end_labels, count(*) as count
                    ORDER BY count DESC
                    LIMIT 5
                """)
                
                logging.info(f"\n  {rel_type} ({count:,} 个关系):")
                for record in pattern_result:
                    start_labels = ':'.join(record['start_labels'])
                    end_labels = ':'.join(record['end_labels'])
                    rel_count = record['count']
                    logging.info(f"    ({start_labels})-[:{rel_type}]->({end_labels}): {rel_count:,}")
    
    def export_clean_schema_report(self, filename="clean_schema_report.txt"):
        """导出干净的schema报告到文件"""
        import sys
        from io import StringIO
        
        # 重定向输出到字符串
        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()
        
        try:
            self.show_clean_schema()
            print("\n" + "="*60 + "\n")
            self.show_detailed_schema()
        finally:
            sys.stdout = old_stdout
        
        # 写入文件
        report_content = captured_output.getvalue()
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("Neo4j 数据库 Schema 报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {logging.Formatter().formatTime(logging.LogRecord('', 0, '', 0, '', (), None))}\n")
            f.write("说明: 此报告仅显示有数据的标签、关系和属性\n\n")
            f.write(report_content)
        
        logging.info(f"📄 Schema报告已导出到: {filename}")
    
    def compare_with_full_schema(self):
        """对比完整schema和干净schema的差异"""
        with self.driver.session() as session:
            # 获取所有标签
            all_labels_result = session.run("CALL db.labels() YIELD label RETURN label")
            all_labels = set(record['label'] for record in all_labels_result)
            
            # 获取所有属性键
            all_props_result = session.run("CALL db.propertyKeys() YIELD propertyKey RETURN propertyKey")
            all_props = set(record['propertyKey'] for record in all_props_result)
            
            # 获取所有关系类型
            all_rels_result = session.run("CALL db.relationshipTypes() YIELD relationshipType RETURN relationshipType")
            all_rels = set(record['relationshipType'] for record in all_rels_result)
        
        # 获取活跃的元素
        active_labels = set(self.get_active_labels().keys())
        active_props = set(self.get_active_properties().keys())
        active_rels = set(self.get_relationship_types().keys())
        
        # 计算差异
        unused_labels = all_labels - active_labels
        unused_props = all_props - active_props
        unused_rels = all_rels - active_rels
        
        logging.info("🔍 Schema使用情况对比")
        logging.info("=" * 60)
        
        logging.info(f"\n📊 标签统计:")
        logging.info(f"  总标签数: {len(all_labels)}")
        logging.info(f"  活跃标签: {len(active_labels)}")
        logging.info(f"  未使用标签: {len(unused_labels)}")
        if unused_labels:
            logging.info(f"  未使用标签列表: {', '.join(sorted(unused_labels))}")
        
        logging.info(f"\n🔑 属性键统计:")
        logging.info(f"  总属性键数: {len(all_props)}")
        logging.info(f"  活跃属性键: {len(active_props)}")
        logging.info(f"  未使用属性键: {len(unused_props)}")
        
        logging.info(f"\n🔗 关系类型统计:")
        logging.info(f"  总关系类型数: {len(all_rels)}")
        logging.info(f"  活跃关系类型: {len(active_rels)}")
        logging.info(f"  未使用关系类型: {len(unused_rels)}")
        if unused_rels:
            logging.info(f"  未使用关系类型列表: {', '.join(sorted(unused_rels))}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='查看干净的Neo4j Schema')
    parser.add_argument('--overview', action='store_true', help='显示schema概览')
    parser.add_argument('--detailed', action='store_true', help='显示详细schema信息')
    parser.add_argument('--compare', action='store_true', help='对比完整schema和活跃schema')
    parser.add_argument('--export', type=str, help='导出报告到文件')
    
    args = parser.parse_args()
    
    viewer = CleanSchemaViewer(NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)
    
    try:
        if args.detailed:
            viewer.show_detailed_schema()
        elif args.compare:
            viewer.compare_with_full_schema()
        elif args.export:
            viewer.export_clean_schema_report(args.export)
        elif args.overview:
            viewer.show_clean_schema()
        else:
            # 默认显示概览
            viewer.show_clean_schema()
    
    except Exception as e:
        logging.error(f"❌ 执行过程中发生错误: {e}")
    finally:
        viewer.close()

if __name__ == "__main__":
    main()

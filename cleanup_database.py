#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库清理脚本
- 清理重复的属性
- 标准化现有数据
- 移除无效数据
"""

import logging
from neo4j import GraphDatabase
from config import NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD, COMPANY_FIELD_MAPPING

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class DatabaseCleaner:
    """数据库清理器"""
    
    def __init__(self, uri, user, password):
        try:
            self.driver = GraphDatabase.driver(uri, auth=(user, password))
            self.driver.verify_connectivity()
            logging.info(f"✅ 成功连接到Neo4j数据库: {uri}")
        except Exception as e:
            logging.error(f"❌ 无法连接到Neo4j数据库: {e}")
            raise
    
    def close(self):
        if self.driver:
            self.driver.close()
            logging.info("🔒 Neo4j数据库连接已关闭")
    
    def cleanup_company_properties(self):
        """清理公司节点的重复属性"""
        with self.driver.session() as session:
            # 获取所有公司节点
            companies = session.run("MATCH (c:Company) RETURN c").data()
            
            logging.info(f"开始清理 {len(companies)} 个公司节点的属性...")
            
            for company_record in companies:
                company = company_record['c']
                company_id = company.get('tianyancha_id')
                
                if not company_id:
                    continue
                
                # 标准化属性
                standardized_props = self._standardize_company_properties(dict(company))
                
                # 更新节点
                session.run("""
                    MATCH (c:Company {tianyancha_id: $company_id})
                    SET c = $props
                    SET c.cleaned_at = datetime()
                """, company_id=company_id, props=standardized_props)
            
            logging.info("✅ 公司节点属性清理完成")
    
    def _standardize_company_properties(self, original_props):
        """标准化公司属性"""
        standardized = {}
        
        # 保留tianyancha_id
        if 'tianyancha_id' in original_props:
            standardized['tianyancha_id'] = original_props['tianyancha_id']
        
        # 保留时间戳
        for time_field in ['created_at', 'updated_at', 'cleaned_at']:
            if time_field in original_props:
                standardized[time_field] = original_props[time_field]
        
        # 映射中文字段到英文字段
        for chinese_key, english_key in COMPANY_FIELD_MAPPING.items():
            if chinese_key in original_props:
                value = original_props[chinese_key]
                if self._is_valid_value(value):
                    standardized[english_key] = value
        
        # 保留已经是英文的标准字段
        standard_english_fields = set(COMPANY_FIELD_MAPPING.values())
        for key, value in original_props.items():
            if key in standard_english_fields and self._is_valid_value(value):
                standardized[key] = value
        
        # 特殊处理
        standardized = self._process_special_company_fields(standardized)
        
        return standardized
    
    def _process_special_company_fields(self, props):
        """处理特殊字段"""
        # 处理合伙企业标识
        company_type = props.get('company_type', '')
        if '合伙' in company_type:
            props['is_partnership'] = True
        else:
            props['is_partnership'] = False
        
        # 处理标签字段
        if 'tags' in props and isinstance(props['tags'], str):
            props['tags'] = [tag.strip() for tag in props['tags'].split('|') if tag.strip()]
        
        return props
    
    def _is_valid_value(self, value):
        """检查值是否有效"""
        if value is None:
            return False
        if isinstance(value, str):
            return value.strip() != '' and value.strip() not in ['-', '暂无', 'null', 'None']
        return True
    
    def remove_duplicate_relationships(self):
        """移除重复的关系"""
        with self.driver.session() as session:
            # 移除重复的HOLDS_SHARE关系
            session.run("""
                MATCH (a)-[r:HOLDS_SHARE]->(b)
                WITH a, b, collect(r) as rels
                WHERE size(rels) > 1
                FOREACH (rel in tail(rels) | DELETE rel)
            """)
            
            # 移除重复的INVESTS_IN关系
            session.run("""
                MATCH (a)-[r:INVESTS_IN]->(b)
                WITH a, b, collect(r) as rels
                WHERE size(rels) > 1
                FOREACH (rel in tail(rels) | DELETE rel)
            """)
            
            logging.info("✅ 重复关系清理完成")
    
    def update_relationship_types(self):
        """更新关系类型，将投资关系从HOLDS_SHARE改为INVESTS_IN"""
        with self.driver.session() as session:
            # 查找所有从公司到公司的HOLDS_SHARE关系，如果有投资相关属性，则转换为INVESTS_IN
            result = session.run("""
                MATCH (source:Company)-[r:HOLDS_SHARE]->(target:Company)
                WHERE r.investment_percentage IS NOT NULL OR r.investment_status IS NOT NULL
                RETURN source, target, r
            """)
            
            count = 0
            for record in result:
                source = record['source']
                target = record['target']
                old_rel = record['r']
                
                # 创建新的INVESTS_IN关系
                session.run("""
                    MATCH (source:Company {tianyancha_id: $source_id})
                    MATCH (target:Company {tianyancha_id: $target_id})
                    MERGE (source)-[r:INVESTS_IN]->(target)
                    SET r += $props
                """, 
                source_id=source['tianyancha_id'],
                target_id=target['tianyancha_id'],
                props=dict(old_rel))
                
                # 删除旧关系
                session.run("""
                    MATCH (source:Company {tianyancha_id: $source_id})-[r:HOLDS_SHARE]->(target:Company {tianyancha_id: $target_id})
                    WHERE r.investment_percentage IS NOT NULL OR r.investment_status IS NOT NULL
                    DELETE r
                """,
                source_id=source['tianyancha_id'],
                target_id=target['tianyancha_id'])
                
                count += 1
            
            logging.info(f"✅ 已转换 {count} 个投资关系")
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        with self.driver.session() as session:
            # 统计节点数量
            company_count = session.run("MATCH (c:Company) RETURN count(c) as count").single()['count']
            person_count = session.run("MATCH (p:Person) RETURN count(p) as count").single()['count']
            
            # 统计关系数量
            holds_share_count = session.run("MATCH ()-[r:HOLDS_SHARE]->() RETURN count(r) as count").single()['count']
            invests_in_count = session.run("MATCH ()-[r:INVESTS_IN]->() RETURN count(r) as count").single()['count']
            
            # 统计清理过的节点
            cleaned_count = session.run("MATCH (c:Company) WHERE c.cleaned_at IS NOT NULL RETURN count(c) as count").single()['count']
            
            report = f"""
数据库清理报告
==============
节点统计:
- 公司节点: {company_count}
- 个人节点: {person_count}

关系统计:
- 持股关系 (HOLDS_SHARE): {holds_share_count}
- 投资关系 (INVESTS_IN): {invests_in_count}

清理统计:
- 已清理的公司节点: {cleaned_count}
- 清理完成率: {cleaned_count/company_count*100:.1f}%
"""
            
            logging.info(report)
            return report

def main():
    """主函数"""
    cleaner = DatabaseCleaner(NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)
    
    try:
        logging.info("🧹 开始数据库清理...")
        
        # 1. 清理公司属性
        cleaner.cleanup_company_properties()
        
        # 2. 移除重复关系
        cleaner.remove_duplicate_relationships()
        
        # 3. 更新关系类型
        cleaner.update_relationship_types()
        
        # 4. 生成报告
        cleaner.generate_cleanup_report()
        
        logging.info("✅ 数据库清理完成!")
        
    except Exception as e:
        logging.error(f"❌ 清理过程中发生错误: {e}")
    finally:
        cleaner.close()

if __name__ == "__main__":
    main()

# config.py

# --- Neo4j 数据库连接配置 ---
# 请根据您的Neo4j数据库实例修改以下配置
# 默认的Bolt端口是 7687
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "zhiyikeji123" # 请务必修改为您的Neo4j密码

# --- 数据模型配置 ---
# 标准化的属性映射，避免中英文重复
COMPANY_FIELD_MAPPING = {
    # 基础信息映射 (中文键 -> 英文键)
    '企业名称': 'name',
    '经营状态': 'business_status',
    '法定代表人': 'legal_representative',
    '注册资本': 'registered_capital',
    '成立日期': 'establishment_date',
    '统一社会信用代码': 'credit_code',
    '企业类型': 'company_type',
    '行业': 'industry',
    '经营范围': 'business_scope',
    '注册地址': 'registered_address',
    '营业期限': 'business_term',
    '工商注册号': 'registration_number',
    '组织机构代码': 'organization_code',
    '纳税人识别号': 'taxpayer_id',
    '标签': 'tags',
    '天眼评分': 'tianyancha_score',
    '参保人数': 'insured_count',
    '人员规模': 'staff_size',
    '实缴资本': 'paid_capital',
    '曾用名': 'former_names',
    '英文名称': 'english_name',
    '登记机关': 'registration_authority',
    '核准日期': 'approval_date',
    '纳税人资质': 'taxpayer_qualification',
    '主要经营场所': 'main_business_location',
    '住所': 'domicile',
    '执行事务合伙人': 'executive_partner',
    '分支机构参保人数': 'branch_insured_count',
    '投资人': 'investor',
    '出资额': 'capital_contribution',
    '分支机构数量': 'branch_count',
    '对外投资数量': 'investment_count',
    '受益所有人': 'beneficial_owners',
    '实际控制人': 'actual_controllers'
}

# 股东关系属性映射
SHAREHOLDER_FIELD_MAPPING = {
    '股东名称': 'shareholder_name',
    '持股比例': 'percentage',
    '认缴出资额万元': 'subscribed_amount',
    '间接持股比例': 'indirect_percentage',
    '认缴出资日期': 'subscription_date',
    '首次持股日期': 'first_holding_date',
    '股份类型': 'share_type',
    '持股数': 'share_count',
    '关联产品机构': 'related_organization'
}

# 投资关系属性映射
INVESTMENT_FIELD_MAPPING = {
    '被投公司名称': 'invested_company_name',
    '投资占比': 'investment_percentage',
    '状态': 'investment_status',
    '注册资本': 'registered_capital',
    '成立日期': 'establishment_date'
}

# 受益所有人字段映射
BENEFICIAL_OWNER_FIELD_MAPPING = {
    '受益所有人名称': 'beneficial_owner_name',
    '最终受益股份': 'final_beneficial_share',
    '受益类型': 'beneficial_type',
    '任职类型': 'position_type',
    '判定理由': 'determination_reason',
    '受益所有人链接': 'beneficial_owner_url'
}

# 实际控制人字段映射
ACTUAL_CONTROLLER_FIELD_MAPPING = {
    '实际控制人名称': 'actual_controller_name',
    '直接持股比例': 'direct_shareholding_ratio',
    '总持股比例': 'total_shareholding_ratio',
    '实际控制人链接': 'actual_controller_url'
}
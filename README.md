# 天眼查股权穿透爬虫

> **零配置 · 动态渲染 · 图数据库 · 股权树穿透分析**

---

## ✨ 项目亮点

1. **双引擎爬取**：Requests + Selenium（自动管理 ChromeDriver），动态页面数据完整抓取。
2. **股权树 BFS 穿透**：支持 *up / down / both* 三种方向，深度可自定义。
3. **企业标签识别**：自动解析天眼查页面 *投资机构 / 国有资本 / 上市状态* 等标签。
4. **Neo4j 图数据库**：节点去重、关系合并，天然支持复杂股权网络查询与可视化。
5. **缓存与增量更新**：同一天内已抓取节点可跳过，显著节省请求次数。
6. **多格式输出**：JSON + CSV（基础信息 / 股东 / 投资）。
7. **一键安装脚本**：`install.py` 自动区分核心及可选依赖。

---

## 🗂️ 目录结构

```
shareholder/
├── tianyancha_scraper.py      # 单公司信息爬虫（核心解析逻辑）
├── tree_crawler.py            # 股权树调度器 (BFS)
├── graph_manager.py           # Neo4j 读写封装
├── run_crawler.py             # CLI 入口：批量/树形抓取
├── config.py                  # Neo4j 连接配置
├── install.py                 # 一键安装依赖
├── requirements.txt           # 依赖列表
├── companies.to.search.txt    # 示例公司清单
└── README.md                  # 使用说明 (本文档)
```

---

## 🚀 快速开始

### 1. 安装依赖

```bash
# 推荐：自动安装 + 环境测试
python install.py

# 或手动安装完整依赖
pip install -r requirements.txt
```

### 2. 单公司测试

```bash
# 仅基础信息（无需 Selenium）
python tianyancha_scraper.py --url "https://www.tianyancha.com/company/22822" --no-selenium

# 完整信息（含股东 / 投资 / 标签）
python tianyancha_scraper.py --url "https://www.tianyancha.com/company/22822"
```

### 3. 股权树穿透

```bash
# 从公司名称出发，向上穿透 4 层
python run_crawler.py -n "小米科技有限责任公司" -d 4 --direction up

# 双向 (股东 + 投资) 穿透，强制刷新并使用 Selenium
python run_crawler.py -n "华为技术有限公司" -d 3 --direction both --force

# 批量公司（文件形式）
python run_crawler.py -f companies.to.search.txt -d 2 --direction up

# 直接从URL列表批量抓取（跳过搜索步骤）
python run_crawler.py -u "https://www.tianyancha.com/company/22822" "https://www.tianyancha.com/company/15964459" -d 3

# 从URL文件批量抓取
python run_crawler.py --url-file company_urls.txt -d 4 --direction both
```

---

## ⚙️ 关键参数（run_crawler.py）

| 参数 | 说明 | 默认 |
|------|------|------|
| `-n / --names` | 一个或多个公司名称 | - |
| `-f / --file` | 存放公司名称的文本文件 | - |
| `-d / --depth` | 穿透层数 | 4 |
| `--direction` | up / down / both | up |
| `--force` | 忽略缓存（总是重新抓取） | False |
| `--no-selenium` | 禁用 Selenium（速度更快，数据可能不完整） | False |
| `--headless` | Selenium 无头模式 | False |

---

## 🗄️ 输出文件

执行成功后会在当前目录生成：

```
<前缀>_data.json            # 全量数据
<前缀>_basic_info.csv       # 企业基础信息
<前缀>_shareholders.csv     # 股东信息（上游）
<前缀>_investments.csv      # 对外投资（下游，需 --direction down/both）
```

其中基础信息 CSV 包含新增列 **"标签"**，示例：`投资机构|A股(正常上市)|国有资本`。

---

## 🗃️ Neo4j 使用

1. 在本地或服务器安装 Neo4j 5.x，并修改 `config.py` 中的 `NEO4J_URI / USER / PASSWORD`。
2. 浏览器打开 `http://localhost:7474`，即可实时查看节点与 `HOLDS_SHARE` 关系。
3. 示例查询：

```cypher
MATCH (p:Person)-[r:HOLDS_SHARE]->(c:Company {name:"小米科技有限责任公司"})
RETURN p,c,r
```

> 提示：如企业类型含"合伙"，节点会自动打上 `:Partnership` 额外标签。

---

## 🧑‍💻 开发指南

- **企业标签解析**：代码位于 `TianyanchaCompanyScraper.parse_company_tags`，若天眼查页面结构调整，可在此处兼容。  
- **增量策略**：`GraphManager.check_node_is_recent` 控制缓存天数（默认 1 天）。  
- **自定义关系**：在 `graph_manager.py` 的 Cypher 语句中添加 / 修改 MERGE 逻辑即可。  
- **日志级别**：修改 `logging.basicConfig(level=logging.INFO, …)` 调整输出。

---

## ❓ 常见问题

| 问题 | 解决方案 |
|------|----------|
| **验证码/滑块拦截** | 确保使用 Selenium + 登录同一浏览器调试端口；必要时人工滑动后继续运行 |
| **ChromeDriver 版本不匹配** | 删除 `~/.wdm` 目录或手动 `pip install --upgrade webdriver-manager` |
| **Neo4j 连接失败** | 检查 `bolt://` 地址、防火墙及 `config.py` 用户密码 |

---

## 📄 License

本仓库仅供学习与研究，严禁用于任何商业或非法目的；使用前请自行确保符合目标网站的使用条款。 
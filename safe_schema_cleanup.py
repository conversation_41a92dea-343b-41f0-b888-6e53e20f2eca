#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的Schema清理脚本
- 分析未使用的属性键和标签
- 提供清理建议，但不强制删除
- 生成清理后的预期效果
"""

import logging
from neo4j import GraphDatabase
from config import NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SafeSchemaAnalyzer:
    """安全的Schema分析器"""
    
    def __init__(self, uri, user, password):
        try:
            self.driver = GraphDatabase.driver(uri, auth=(user, password))
            self.driver.verify_connectivity()
            logging.info(f"✅ 成功连接到Neo4j数据库: {uri}")
        except Exception as e:
            logging.error(f"❌ 无法连接到Neo4j数据库: {e}")
            raise
    
    def close(self):
        if self.driver:
            self.driver.close()
            logging.info("🔒 Neo4j数据库连接已关闭")
    
    def analyze_property_keys(self):
        """分析属性键使用情况"""
        with self.driver.session() as session:
            # 获取所有属性键
            result = session.run("CALL db.propertyKeys() YIELD propertyKey RETURN propertyKey ORDER BY propertyKey")
            property_keys = [record['propertyKey'] for record in result]
            
            logging.info(f"📊 发现 {len(property_keys)} 个属性键")
            
            # 分析每个属性键的使用情况
            usage_stats = {}
            for key in property_keys:
                try:
                    # 统计使用次数
                    count_result = session.run(f"""
                        MATCH (n) 
                        WHERE n.`{key}` IS NOT NULL 
                        RETURN count(n) as count
                    """)
                    count = count_result.single()['count']
                    usage_stats[key] = count
                except Exception as e:
                    logging.warning(f"⚠️  无法统计属性键 '{key}': {e}")
                    usage_stats[key] = 0
            
            return usage_stats
    
    def analyze_labels(self):
        """分析标签使用情况"""
        with self.driver.session() as session:
            # 获取所有标签
            result = session.run("CALL db.labels() YIELD label RETURN label ORDER BY label")
            labels = [record['label'] for record in result]
            
            # 统计每个标签的节点数量
            label_stats = {}
            for label in labels:
                count_result = session.run(f"MATCH (n:{label}) RETURN count(n) as count")
                count = count_result.single()['count']
                label_stats[label] = count
            
            return label_stats
    
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        logging.info("📋 生成综合Schema分析报告...")
        
        # 分析属性键
        property_stats = self.analyze_property_keys()
        
        # 分析标签
        label_stats = self.analyze_labels()
        
        # 分类属性键
        used_properties = {k: v for k, v in property_stats.items() if v > 0}
        unused_properties = {k: v for k, v in property_stats.items() if v == 0}
        
        # 分类标签
        used_labels = {k: v for k, v in label_stats.items() if v > 0}
        unused_labels = {k: v for k, v in label_stats.items() if v == 0}
        
        # 生成报告
        report = f"""
╔══════════════════════════════════════════════════════════════╗
║                    Neo4j Schema 分析报告                      ║
╚══════════════════════════════════════════════════════════════╝

📊 总体统计:
  - 总属性键数量: {len(property_stats)}
  - 已使用属性键: {len(used_properties)}
  - 未使用属性键: {len(unused_properties)}
  - 总标签数量: {len(label_stats)}
  - 已使用标签: {len(used_labels)}
  - 未使用标签: {len(unused_labels)}

🏷️  标签使用情况:
"""
        
        for label, count in sorted(label_stats.items()):
            status = "✅ 活跃" if count > 0 else "❌ 空标签"
            report += f"  {label:<15}: {count:>6} 个节点 ({status})\n"
        
        report += f"\n🔑 属性键使用情况 (前20个最常用):\n"
        sorted_props = sorted(used_properties.items(), key=lambda x: x[1], reverse=True)
        for key, count in sorted_props[:20]:
            report += f"  {key:<25}: {count:>6} 次使用\n"
        
        if unused_properties:
            report += f"\n❌ 未使用的属性键 ({len(unused_properties)}个):\n"
            for key in sorted(unused_properties.keys()):
                report += f"  - {key}\n"
        
        report += f"""
💡 清理建议:

1. 标签清理:
   - 保留: {', '.join(used_labels.keys())}
   - 可忽略的空标签: {', '.join(unused_labels.keys()) if unused_labels else '无'}

2. 属性键清理:
   - 核心属性键 (>100次使用): {len([k for k, v in used_properties.items() if v > 100])} 个
   - 稀有属性键 (<10次使用): {len([k for k, v in used_properties.items() if v < 10])} 个
   - 未使用属性键: {len(unused_properties)} 个

3. Neo4j Schema清理真相:
   ❌ 误解: Neo4j会自动清理未使用的标签和属性键
   ✅ 事实: Neo4j永久保留所有创建过的标签和属性键
   
4. 为什么不自动清理:
   - 标签和属性键是数据库schema的一部分
   - 保留可以避免重复创建的开销
   - 防止意外删除可能在未来使用的schema元素
   - 向后兼容性考虑

5. 如何真正清理:
   - 方法1: 重建数据库 (导出数据 → 删除数据库 → 重新导入)
   - 方法2: 接受现状 (这些空标签和属性键不影响性能)
   - 方法3: 使用专业工具 (如Neo4j Admin工具)

6. 推荐做法:
   ✅ 接受现状 - 空标签和未使用属性键不会影响查询性能
   ✅ 关注数据质量 - 确保使用的属性有意义且一致
   ✅ 定期清理数据 - 删除无用的节点和关系
   ❌ 不要强制清理schema - 风险大于收益
"""
        
        logging.info(report)
        return report
    
    def explain_neo4j_behavior(self):
        """解释Neo4j的Schema行为"""
        explanation = """
🔍 Neo4j Schema管理机制详解:

1. 标签 (Labels):
   - 一旦创建，永久存在于数据库schema中
   - 即使删除所有使用该标签的节点，标签仍然保留
   - 重启Neo4j不会清理未使用的标签

2. 属性键 (Property Keys):
   - 一旦创建，永久存在于数据库schema中
   - 即使删除所有使用该属性的节点，属性键仍然保留
   - 重启Neo4j不会清理未使用的属性键

3. 关系类型 (Relationship Types):
   - 同样永久保留，不会自动清理

4. 为什么这样设计:
   - 性能优化: 避免重复创建schema元素的开销
   - 一致性: 保证schema的稳定性
   - 兼容性: 防止意外删除影响应用程序

5. 影响评估:
   ✅ 对查询性能: 几乎无影响
   ✅ 对存储空间: 影响极小 (schema元数据很小)
   ❌ 对视觉整洁: 可能看起来混乱

6. 最佳实践:
   - 在开发阶段规划好schema设计
   - 使用一致的命名规范
   - 定期清理无用的数据，而不是schema
   - 接受schema的"历史痕迹"
"""
        
        logging.info(explanation)
        return explanation

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='安全的Neo4j Schema分析')
    parser.add_argument('--report', action='store_true', help='生成综合分析报告')
    parser.add_argument('--explain', action='store_true', help='解释Neo4j Schema行为')
    parser.add_argument('--properties', action='store_true', help='仅分析属性键')
    parser.add_argument('--labels', action='store_true', help='仅分析标签')
    
    args = parser.parse_args()
    
    analyzer = SafeSchemaAnalyzer(NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)
    
    try:
        if args.explain:
            analyzer.explain_neo4j_behavior()
        elif args.properties:
            stats = analyzer.analyze_property_keys()
            logging.info("🔑 属性键统计:")
            for key, count in sorted(stats.items(), key=lambda x: x[1], reverse=True):
                status = "✅ 使用中" if count > 0 else "❌ 未使用"
                logging.info(f"  {key:<25}: {count:>6} 次 ({status})")
        elif args.labels:
            stats = analyzer.analyze_labels()
            logging.info("🏷️  标签统计:")
            for label, count in sorted(stats.items()):
                status = "✅ 活跃" if count > 0 else "❌ 空标签"
                logging.info(f"  {label:<15}: {count:>6} 个节点 ({status})")
        elif args.report:
            analyzer.generate_comprehensive_report()
        else:
            # 默认行为
            analyzer.generate_comprehensive_report()
    
    except Exception as e:
        logging.error(f"❌ 执行过程中发生错误: {e}")
    finally:
        analyzer.close()

if __name__ == "__main__":
    main()

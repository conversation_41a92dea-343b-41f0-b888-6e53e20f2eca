#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股权树抓取任务主入口
- 解析命令行参数
- 启动TreeCrawler
- 支持企业名称和URL两种输入方式
"""
import argparse
from tree_crawler import TreeCrawler

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='天眼查股权树穿透抓取工具',
        formatter_class=argparse.RawTextHelpFormatter  # 美化帮助信息的显示
    )
    
    # === 输入方式 1: 企业名称 ===
    parser.add_argument('-n', '--names', type=str, nargs='+', 
                        help='一个或多个要查询的起始公司名称。\n示例: --names "小米科技有限责任公司" "华为技术有限公司"')
    
    parser.add_argument('-f', '--file', type=str, 
                        help='包含公司名称列表的文本文件路径（每行一个公司名）。')

    # === 输入方式 2: 直接URL ===
    parser.add_argument('-u', '--urls', type=str, nargs='+',
                        help='一个或多个天眼查企业详情页URL。\n示例: --urls "https://www.tianyancha.com/company/22822" "https://www.tianyancha.com/company/15964459"')
    
    parser.add_argument('--url-file', type=str,
                        help='包含天眼查URL列表的文本文件路径（每行一个URL）。')

    # === 抓取参数 ===
    parser.add_argument('-d', '--depth', type=int, default=4,
                        help='向上穿透的层数（深度）。默认为 4。')

    parser.add_argument('--direction', type=str, default='up', choices=['up', 'down', 'both'],
                        help='抓取方向: up (仅股东), down (仅投资), both (双向)。默认为 up。')

    parser.add_argument('--force', action='store_true',
                        help='强制刷新，忽略缓存，从网页重新抓取并覆盖数据库信息。')

    # === 浏览器参数 ===
    parser.add_argument('--no-selenium', action='store_true',
                        help='【不推荐】禁用Selenium，使用基础模式。这很可能导致抓取失败。')
    
    parser.add_argument('--headless', action='store_true',
                        help='【仅在Selenium模式下生效】在无头模式下运行浏览器，不显示界面。')
    
    args = parser.parse_args()

    # === 收集输入数据 ===
    company_names_to_search = []
    urls_to_crawl = []
    
    # 收集企业名称
    if args.names:
        company_names_to_search.extend(args.names)
    
    if args.file:
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip()]
                company_names_to_search.extend(lines)
            print(f"📂 从文件 '{args.file}' 加载了 {len(lines)} 个公司名称。")
        except FileNotFoundError:
            print(f"❌ 文件未找到: {args.file}")
            return
    
    # 收集URL
    if args.urls:
        urls_to_crawl.extend(args.urls)
    
    if args.url_file:
        try:
            with open(args.url_file, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip() and 'tianyancha.com' in line]
                urls_to_crawl.extend(lines)
            print(f"📂 从文件 '{args.url_file}' 加载了 {len(lines)} 个URL。")
        except FileNotFoundError:
            print(f"❌ 文件未找到: {args.url_file}")
            return
    
    # 验证输入
    if not company_names_to_search and not urls_to_crawl:
        print("❌ 错误: 请至少使用以下参数之一提供输入：")
        print("   企业名称: --names 或 --file")
        print("   企业URL:  --urls 或 --url-file")
        parser.print_help()
        return

    # === 启动爬虫 ===
    try:
        # 修改: 默认使用Selenium，除非用户明确指定--no-selenium
        use_selenium = not args.no_selenium
        
        # 当且仅当使用Selenium且用户指定了--headless时，才在无头模式下运行
        is_headless = True if not use_selenium or args.headless else False

        crawler = TreeCrawler(use_selenium=use_selenium, headless=is_headless)
        
        # 根据输入类型选择不同的启动方式
        if urls_to_crawl:
            print(f"🚀 开始直接从 {len(urls_to_crawl)} 个URL进行股权穿透抓取...")
            print(f"📊 抓取参数: 深度={args.depth}, 方向={args.direction}, 强制刷新={args.force}")
            print("=" * 60)
            
            crawler.crawl(
                urls_to_crawl,
                max_depth=args.depth,
                direction=args.direction,
                force_refresh=args.force
            )
        
        if company_names_to_search:
            print(f"🔍 开始从 {len(company_names_to_search)} 个企业名称搜索并抓取...")
            print(f"📊 抓取参数: 深度={args.depth}, 方向={args.direction}, 强制刷新={args.force}")
            print("=" * 60)
            
            crawler.search_and_start(
                company_names_to_search, 
                max_depth=args.depth, 
                direction=args.direction,
                force_refresh=args.force
            )
            
    except Exception as e:
        print(f"💥 主程序发生无法处理的错误: {e}")

if __name__ == "__main__":
    main() 
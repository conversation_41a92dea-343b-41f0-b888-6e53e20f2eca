#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制清理Neo4j Schema脚本
- 手动清理未使用的标签和属性键
- 重建干净的数据库schema
"""

import logging
import os
import tempfile
from neo4j import GraphDatabase
from config import NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SchemaForceCleanup:
    """强制Schema清理器"""
    
    def __init__(self, uri, user, password):
        try:
            self.driver = GraphDatabase.driver(uri, auth=(user, password))
            self.driver.verify_connectivity()
            logging.info(f"✅ 成功连接到Neo4j数据库: {uri}")
        except Exception as e:
            logging.error(f"❌ 无法连接到Neo4j数据库: {e}")
            raise
    
    def close(self):
        if self.driver:
            self.driver.close()
            logging.info("🔒 Neo4j数据库连接已关闭")
    
    def export_data(self, export_file):
        """导出所有数据到Cypher文件"""
        logging.info("📤 开始导出数据...")
        
        with self.driver.session() as session:
            with open(export_file, 'w', encoding='utf-8') as f:
                f.write("// Neo4j数据导出文件\n")
                f.write("// 自动生成，用于schema清理后的数据恢复\n\n")
                
                # 1. 导出所有Company节点
                logging.info("  📊 导出Company节点...")
                companies = session.run("""
                    MATCH (c:Company)
                    RETURN c.tianyancha_id as id, properties(c) as props, labels(c) as labels
                """).data()
                
                for company in companies:
                    props = company['props']
                    labels = ':'.join(company['labels'])
                    
                    # 构建属性字符串
                    prop_parts = []
                    for key, value in props.items():
                        if isinstance(value, str):
                            # 转义字符串中的引号
                            escaped_value = value.replace('"', '\\"').replace('\n', '\\n')
                            prop_parts.append(f'{key}: "{escaped_value}"')
                        elif isinstance(value, list):
                            # 处理列表（如tags）
                            list_str = ', '.join([f'"{item}"' for item in value])
                            prop_parts.append(f'{key}: [{list_str}]')
                        else:
                            prop_parts.append(f'{key}: {value}')
                    
                    props_str = ', '.join(prop_parts)
                    f.write(f"MERGE (c:{labels} {{tianyancha_id: {props['tianyancha_id']}}}) SET c = {{{props_str}}};\n")
                
                # 2. 导出所有Person节点
                logging.info("  👤 导出Person节点...")
                persons = session.run("""
                    MATCH (p:Person)
                    RETURN properties(p) as props
                """).data()
                
                for person in persons:
                    props = person['props']
                    prop_parts = []
                    
                    for key, value in props.items():
                        if isinstance(value, str):
                            escaped_value = value.replace('"', '\\"').replace('\n', '\\n')
                            prop_parts.append(f'{key}: "{escaped_value}"')
                        else:
                            prop_parts.append(f'{key}: {value}')
                    
                    props_str = ', '.join(prop_parts)
                    
                    # 根据是否有tianyancha_id选择主键
                    if 'tianyancha_id' in props:
                        f.write(f"MERGE (p:Person {{tianyancha_id: {props['tianyancha_id']}}}) SET p = {{{props_str}}};\n")
                    else:
                        f.write(f"MERGE (p:Person {{name: \"{props['name']}\"}}) SET p = {{{props_str}}};\n")
                
                # 3. 导出所有关系
                logging.info("  🔗 导出关系...")
                relationships = session.run("""
                    MATCH (a)-[r]->(b)
                    RETURN 
                        labels(a) as start_labels,
                        properties(a) as start_props,
                        type(r) as rel_type,
                        properties(r) as rel_props,
                        labels(b) as end_labels,
                        properties(b) as end_props
                """).data()
                
                for rel in relationships:
                    # 构建起始节点匹配
                    start_labels = ':'.join(rel['start_labels'])
                    end_labels = ':'.join(rel['end_labels'])
                    
                    # 构建节点标识
                    start_id = self._get_node_identifier(rel['start_props'], start_labels)
                    end_id = self._get_node_identifier(rel['end_props'], end_labels)
                    
                    # 构建关系属性
                    rel_props = rel['rel_props']
                    if rel_props:
                        prop_parts = []
                        for key, value in rel_props.items():
                            if isinstance(value, str):
                                escaped_value = value.replace('"', '\\"')
                                prop_parts.append(f'{key}: "{escaped_value}"')
                            else:
                                prop_parts.append(f'{key}: {value}')
                        props_str = ' {' + ', '.join(prop_parts) + '}'
                    else:
                        props_str = ''
                    
                    f.write(f"MATCH (a:{start_labels} {start_id}), (b:{end_labels} {end_id}) MERGE (a)-[:{rel['rel_type']}{props_str}]->(b);\n")
        
        logging.info(f"✅ 数据导出完成: {export_file}")
    
    def _get_node_identifier(self, props, labels):
        """获取节点的唯一标识符"""
        if 'tianyancha_id' in props:
            return f"{{tianyancha_id: {props['tianyancha_id']}}}"
        elif 'name' in props:
            escaped_name = props['name'].replace('"', '\\"')
            return f'{{name: "{escaped_name}"}}'
        else:
            # 使用第一个属性作为标识
            key, value = next(iter(props.items()))
            if isinstance(value, str):
                escaped_value = value.replace('"', '\\"')
                return f'{{key}: "{escaped_value}"}}'
            else:
                return f'{{key}: {value}}'
    
    def clear_database(self):
        """清空数据库"""
        logging.info("🗑️  开始清空数据库...")
        
        with self.driver.session() as session:
            # 删除所有关系和节点
            session.run("MATCH (n) DETACH DELETE n")
            
            # 删除所有索引
            indexes = session.run("SHOW INDEXES").data()
            for index in indexes:
                if index.get('type') != 'LOOKUP':  # 保留系统索引
                    try:
                        session.run(f"DROP INDEX {index['name']}")
                        logging.info(f"  🗑️  删除索引: {index['name']}")
                    except Exception as e:
                        logging.warning(f"  ⚠️  删除索引失败: {e}")
            
            # 删除所有约束
            constraints = session.run("SHOW CONSTRAINTS").data()
            for constraint in constraints:
                try:
                    session.run(f"DROP CONSTRAINT {constraint['name']}")
                    logging.info(f"  🗑️  删除约束: {constraint['name']}")
                except Exception as e:
                    logging.warning(f"  ⚠️  删除约束失败: {e}")
        
        logging.info("✅ 数据库清空完成")
    
    def import_data(self, import_file):
        """从Cypher文件导入数据"""
        logging.info("📥 开始导入数据...")
        
        with open(import_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按行分割并执行
        statements = [line.strip() for line in content.split('\n') if line.strip() and not line.strip().startswith('//')]
        
        with self.driver.session() as session:
            for i, statement in enumerate(statements):
                try:
                    session.run(statement)
                    if (i + 1) % 100 == 0:
                        logging.info(f"  📥 已导入 {i + 1}/{len(statements)} 条语句")
                except Exception as e:
                    logging.error(f"  ❌ 导入语句失败: {statement[:100]}... 错误: {e}")
        
        logging.info("✅ 数据导入完成")
    
    def recreate_indexes(self):
        """重建必要的索引"""
        logging.info("🔧 重建索引...")
        
        with self.driver.session() as session:
            # 为Company节点的tianyancha_id创建唯一约束
            try:
                session.run("CREATE CONSTRAINT company_tianyancha_id_unique FOR (c:Company) REQUIRE c.tianyancha_id IS UNIQUE")
                logging.info("  ✅ 创建Company.tianyancha_id唯一约束")
            except Exception as e:
                logging.warning(f"  ⚠️  创建约束失败: {e}")
            
            # 为Person节点创建索引
            try:
                session.run("CREATE INDEX person_tianyancha_id FOR (p:Person) ON (p.tianyancha_id)")
                logging.info("  ✅ 创建Person.tianyancha_id索引")
            except Exception as e:
                logging.warning(f"  ⚠️  创建索引失败: {e}")
            
            try:
                session.run("CREATE INDEX person_name FOR (p:Person) ON (p.name)")
                logging.info("  ✅ 创建Person.name索引")
            except Exception as e:
                logging.warning(f"  ⚠️  创建索引失败: {e}")
    
    def force_cleanup_schema(self):
        """强制清理schema"""
        # 创建临时文件
        temp_file = tempfile.mktemp(suffix='.cypher')
        
        try:
            logging.info("🚀 开始强制清理Neo4j Schema...")
            
            # 1. 导出数据
            self.export_data(temp_file)
            
            # 2. 清空数据库
            confirm = input("\n⚠️  即将清空整个数据库！确认继续吗？(输入 'YES' 确认): ")
            if confirm != 'YES':
                logging.info("❌ 操作已取消")
                return
            
            self.clear_database()
            
            # 3. 导入数据
            self.import_data(temp_file)
            
            # 4. 重建索引
            self.recreate_indexes()
            
            logging.info("🎉 Schema清理完成！")
            
        except Exception as e:
            logging.error(f"❌ 清理过程中发生错误: {e}")
            logging.info(f"💾 数据备份文件保存在: {temp_file}")
        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    logging.info("🗑️  临时文件已清理")
                except:
                    logging.warning(f"⚠️  无法删除临时文件: {temp_file}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='强制清理Neo4j Schema')
    parser.add_argument('--force-cleanup', action='store_true', help='执行强制清理（危险操作）')
    parser.add_argument('--export-only', type=str, help='仅导出数据到指定文件')
    
    args = parser.parse_args()
    
    cleaner = SchemaForceCleanup(NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)
    
    try:
        if args.export_only:
            cleaner.export_data(args.export_only)
            logging.info(f"✅ 数据已导出到: {args.export_only}")
        elif args.force_cleanup:
            cleaner.force_cleanup_schema()
        else:
            print("""
使用方法:
  python force_cleanup_schema.py --export-only backup.cypher  # 仅导出数据
  python force_cleanup_schema.py --force-cleanup              # 强制清理schema（危险）

⚠️  警告: --force-cleanup 会清空整个数据库！请先备份数据！
            """)
    
    except Exception as e:
        logging.error(f"❌ 执行过程中发生错误: {e}")
    finally:
        cleaner.close()

if __name__ == "__main__":
    main()

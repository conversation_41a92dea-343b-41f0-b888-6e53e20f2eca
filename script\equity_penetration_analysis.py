import argparse
import logging
import sys
import os

# 将项目根目录添加到Python的搜索路径中
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from neo4j import GraphDatabase
from pyecharts import options as opts
from pyecharts.charts import Graph
from urllib.parse import urlparse
from config import NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class Neo4jEquityAnalyzer:
    """使用Neo4j进行股权穿透分析"""

    def __init__(self, uri, user, password):
        try:
            self.driver = GraphDatabase.driver(uri, auth=(user, password))
            self.driver.verify_connectivity()
            logging.info(f"✅ 成功连接到Neo4j数据库: {uri}")
        except Exception as e:
            logging.error(f"❌ 无法连接到Neo4j数据库: {e}")
            raise

    def close(self):
        if self.driver:
            self.driver.close()
            logging.info("🔒 Neo4j数据库连接已关闭")

    def get_penetration_graph(self, start_node_name, threshold=30.0, depth=5):
        """
        从Neo4j执行穿透查询，获取子图数据。
        
        Args:
            start_node_name (str): 起始公司名称.
            threshold (float): 持股比例阈值 (例如 30.0 for 30%).
            depth (int): 穿透深度.
            
        Returns:
            list, list: pyecharts格式的节点和链接列表。
        """
        nodes = {}
        links = []

        # Cypher查询：同时查找上游股东和下游投资
        # 使用 OR 条件匹配路径的起点或终点
        # 使用 apoc.path.subgraphAll 来更高效地获取所有相关的节点和关系
        query = """
            MATCH (startNode) WHERE startNode.name = $start_node
            CALL apoc.path.subgraphAll(startNode, {{
                relationshipFilter: "HOLDS_SHARE>",
                minLevel: 1,
                maxLevel: {depth}
            }}) YIELD nodes, relationships
            WITH startNode, nodes, relationships
            CALL apoc.path.subgraphAll(startNode, {{
                relationshipFilter: "<HOLDS_SHARE",
                minLevel: 1,
                maxLevel: {depth}
            }}) YIELD nodes AS nodes_up, relationships AS rels_up
            
            WITH nodes + nodes_up AS all_nodes, relationships + rels_up AS all_rels
            UNWIND all_nodes as node
            WITH collect(DISTINCT node) as unique_nodes, all_rels
            UNWIND all_rels as rel
            WITH unique_nodes, collect(DISTINCT rel) as unique_rels
            
            // 过滤不满足阈值的关系
            WITH unique_nodes, [r IN unique_rels WHERE toFloat(replace(r.percentage, '%', '')) >= $threshold] AS filtered_rels
            UNWIND unique_nodes as node
            RETURN node, filtered_rels
        """.format(depth=depth)

        with self.driver.session() as session:
            results = list(session.run(query, start_node=start_node_name, threshold=threshold))

            if not results:
                return [], []
            
            # 由于apoc返回所有节点，我们需要从关系中重建图
            final_nodes_from_rels = set()
            processed_rels = []

            # 只处理一次关系
            rels_set = {r.element_id for r in results[0]['filtered_rels']}
            for rel in results[0]['filtered_rels']:
                if rel.element_id in rels_set:
                    source_node = rel.start_node
                    target_node = rel.end_node
                    final_nodes_from_rels.add(source_node)
                    final_nodes_from_rels.add(target_node)
                    
                    link = {
                        'source': source_node.get('name'),
                        'target': target_node.get('name'),
                        'percentage': rel.get('percentage', 'N/A')
                    }
                    processed_rels.append(link)
                    rels_set.remove(rel.element_id)

            # 格式化节点
            for node_data in final_nodes_from_rels:
                node_id = node_data.element_id
                if node_id not in nodes:
                    node_name = node_data.get('name', '未知节点')
                    node_type = 'Person' if 'Person' in node_data.labels else 'Company'
                    nodes[node_id] = {'name': node_name, 'type': node_type}
        
        return list(nodes.values()), processed_rels


    def visualize_graph(self, nodes, links, start_node_name, threshold):
        """
        使用Pyecharts将从Neo4j获取的数据可视化。
        """
        if not nodes:
            logging.warning("🤷 根据查询条件，没有发现可供可视化的数据。")
            return

        nodes_for_pyecharts = []
        for node in nodes:
            node_name = node['name']
            node_type = node['type']
            
            node_size = 25 if node_name == start_node_name else (15 if node_type == 'Company' else 10)
            category = 2 if node_name == start_node_name else (0 if node_type == 'Company' else 1)
            
            nodes_for_pyecharts.append(
                opts.GraphNode(
                    name=node_name,
                    symbol_size=node_size,
                    category=category
                )
            )
        
        links_for_pyecharts = []
        for link in links:
            links_for_pyecharts.append(
                opts.GraphLink(
                    source=link['source'], 
                    target=link['target'], 
                    label_opts=opts.LabelOpts(is_show=True, formatter=link.get('percentage', ''))
                )
            )
        
        categories = [
            opts.GraphCategory(name="公司"), 
            opts.GraphCategory(name="个人"),
            opts.GraphCategory(name="目标公司", itemstyle_opts=opts.ItemStyleOpts(color="#d9534f"))
        ]

        # 清理文件名中的非法字符
        safe_filename = "".join(c for c in start_node_name if c.isalnum() or c in (' ', '_')).rstrip()
        output_filename = f"股权穿透报告_{safe_filename}_threshold_{int(threshold)}.html"

        c = (
            Graph(init_opts=opts.InitOpts(width="1400px", height="900px", bg_color="#fafafa"))
            .add(
                "",
                nodes_for_pyecharts,
                links_for_pyecharts,
                categories=categories,
                repulsion=5000,
                edge_symbol=["", "arrow"],
                layout="force",
                linestyle_opts=opts.LineStyleOpts(curve=0.2, width=1.5)
            )
            .set_global_opts(
                title_opts=opts.TitleOpts(title=f"股权穿透分析 - {start_node_name}", subtitle=f"持股比例阈值: >{threshold}%"),
                legend_opts=opts.LegendOpts(orient="vertical", pos_left="2%", pos_top="20%")
            )
            .render(output_filename)
        )
        
        logging.info(f"✅ 可视化报告已生成: {output_filename}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="基于Neo4j的股权穿透分析工具")
    parser.add_argument("company", type=str, help="要分析的目标公司名称")
    parser.add_argument("-t", "--threshold", type=float, default=30.0, help="持股比例阈值 (例如, 30 代表 30%%)，默认为30.0")
    parser.add_argument("-d", "--depth", type=int, default=5, help="向上和向下穿透的最大层数，默认为5")
    # 允许命令行覆盖配置
    parser.add_argument("--uri", type=str, default=NEO4J_URI, help="Neo4j数据库的URI")
    parser.add_argument("--user", type=str, default=NEO4J_USER, help="Neo4j数据库的用户名")
    parser.add_argument("--password", type=str, default=NEO4J_PASSWORD, help="Neo4j数据库的密码")
    
    args = parser.parse_args()

    try:
        analyzer = Neo4jEquityAnalyzer(args.uri, args.user, args.password)
        nodes_data, links_data = analyzer.get_penetration_graph(args.company, args.threshold, args.depth)
        
        if nodes_data:
            analyzer.visualize_graph(nodes_data, links_data, args.company, args.threshold)
        else:
            logging.info(f"分析完成，但在阈值 >{args.threshold}% 和深度 {args.depth} 下未发现相关股权关系。")
            
        analyzer.close()
    except Exception as e:
        logging.error(f"分析过程中发生严重错误: {e}") 
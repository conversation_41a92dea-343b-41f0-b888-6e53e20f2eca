# 股权网络图数据分析计划

本文档由您的知识图谱专家、前端专家和高级数据分析师制定，旨在深入挖掘项目中的股权网络数据，发现潜在的商业价值。

---

## 1. 数据结构分析 (Data Structure Analysis)

通过分析 `tianyancha_scraper.py` 爬虫脚本和其JSON输出样本，我们明确了当前数据集的核心结构。这是我们进行一切图分析的基础。

-   **图的节点 (Nodes):**
    -   **公司节点**: 包含丰富的属性，如 `企业名称`, `注册资本`, `成立日期`, `行业`, `经营范围` 等。
    -   **个人节点**: 代表自然人股东，如“雷军”。

-   **图的边 (Edges):**
    -   **持股关系 (Shareholding):** 从“股东”指向“被持股公司”。这是网络的核心有向边。
        -   **边属性**: `持股比例`, `认缴出资额`。这些是进行加权分析的关键。
    -   **投资关系 (Investment):** 从“公司”指向“其投资的公司”。这是股权关系的另一个方向。

-   **关键字段小结:**
    -   `shareholders_info`: 定义了股东 -> 公司的关系。
    -   `investment_info`: 定义了公司 -> 对外投资的关系。
    -   `股东链接`: 提供了在股权网络中进行深度遍历（穿透）的技术可行性。

---

## 2. 股权网络分析内容 (Shareholder Network Analysis)

基于上述数据，我们可以开展由浅入深的多种图分析：

### 2.1. 核心分析功能

-   **股权穿透分析 (Ownership Penetration Analysis):**
    -   **向上穿透**: 追溯一家公司的最终实际控制人（Ultimate Beneficial Owner - UBO）。通过递归查询股东，直到找到自然人或国有资产实体。
    -   **向下穿透**: 展示一家公司（或个人）控制的所有底层资产，形成其商业帝国版图。

-   **股权关联路径发现 (Relationship Path Finding):**
    -   计算网络中任意两个实体（公司或个人）之间的所有权路径。这对于揭示隐藏的关联方关系、潜在的利益冲突至关重要。例如，分析A公司和B公司是否通过一连串复杂的交叉持股产生间接关联。

-   **一致行动人识别 (Concert Party Identification):**
    -   通过分析股东的共同投资行为、家族关系（需外部数据）、历史关联，识别出网络中可能存在的一致行动人团体。

-   **循环持股与质押风险分析 (Circular Holdings & Pledge Risk):**
    -   **循环持股**: 检测图中是否存在"A->B->C->A"这样的持股环路。这通常是资本运作的危险信号，可能涉及资本虚增或集团内资金占用。
    -   **股权质押**: (需补充数据) 结合股权出质信息，可以分析关键股东的质押风险及其对整个网络的潜在影响。

### 2.2. 高级分析与模型应用

-   **社区发现 (Community Detection) / 派系分析:**
    -   **目的**: 识别网络中的“子社区”或“派系”，即内部联系紧密但与外部联系相对稀疏的股东与公司集群。
    -   **方法**: 应用 **Louvain**、**Girvan-Newman** 等算法。
    -   **商业价值**: 发现背后由同一资本控制的“XX系”企业集群，理解资本市场的势力范围。

-   **中心性分析 (Centrality Analysis):**
    -   **目的**: 识别网络中的关键节点（公司或个人）。
    -   **方法**:
        -   **度中心性 (Degree Centrality)**: 拥有最多直接投资或被投资关系，是局部交易核心。
        -   **介数中心性 (Betweenness Centrality)**: 处于许多最短路径上的“桥梁”节点，控制着资本和信息的流动。
        -   **特征向量中心性 (Eigenvector Centrality)**: 不仅连接多，而且连接的“邻居”也很重要，是网络中真正有影响力的核心。
    -   **商业价值**: 快速定位网络中的“大佬”和关键“枢纽”公司。

-   **链接预测 (Link Prediction):**
    -   **目的**: 预测网络中未来可能出现的投资关系。
    -   **方法**: 基于网络结构特征（如共同邻居、Jaccard系数等）训练预测模型。
    -   **商业价值**: 用于投资机会发现或潜在风险关系的预警。

-   **图神经网络 (Graph Neural Networks - GNN):**
    -   **目的**: 利用图的深度学习能力，进行更复杂的任务，如节点分类、异常检测等。
    -   **模型**: GCN (Graph Convolutional Network), GAT (Graph Attention Network) 等。
    -   **应用场景**:
        -   **节点分类**: 自动识别企业在网络中的角色（如：核心控股平台、投资工具、实体业务公司）。
        -   **异常检测**: 识别异常的持股模式，用于金融风控或非法集资预警。

---

## 3. 实施建议 (Implementation Plan)

### 3.1. 技术选型

-   **后端/数据处理**: `Python`
    -   **数据处理**: `Pandas`
    -   **图计算**: `NetworkX` (适用于中小型网络分析和原型验证) 或 `Neo4j` / `NebulaGraph` (适用于大型网络存储和高性能查询)。
    -   **GNN**: `PyG` (PyTorch Geometric) 或 `DGL` (Deep Graph Library)。
-   **前端/可视化**:
    -   使用 `ECharts`, `D3.js`, 或 `G6` (AntV) 等库来动态、交互式地展示股权网络图。

### 3.2. 脚本开发计划 (在 `script/` 文件夹下)

1.  **`data_importer.py`**: 编写一个脚本，负责解析所有的 `.json` 文件，并将它们转换并加载到一个统一的图结构中（例如，一个 NetworkX Graph 对象或导入到 Neo4j 数据库）。
2.  **`find_ubo.py`**: 实现股权向上穿透，查找最终控制人的核心算法。
3.  **`find_paths.py`**: 实现任意两点间的关联路径查找。
4.  **`run_community_detection.py`**: 应用 Louvain 算法进行社区发现，并输出结果。
5.  **`run_centrality_analysis.py`**: 计算节点的各类中心性指标，并输出 Top-K 结果。 
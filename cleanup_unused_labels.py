#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理无用标签脚本
- 删除空的或无用的节点标签
- 保持数据库标签结构清晰
"""

import logging
from neo4j import GraphDatabase
from config import NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class LabelCleaner:
    """标签清理器"""
    
    def __init__(self, uri, user, password):
        try:
            self.driver = GraphDatabase.driver(uri, auth=(user, password))
            self.driver.verify_connectivity()
            logging.info(f"✅ 成功连接到Neo4j数据库: {uri}")
        except Exception as e:
            logging.error(f"❌ 无法连接到Neo4j数据库: {e}")
            raise
    
    def close(self):
        if self.driver:
            self.driver.close()
            logging.info("🔒 Neo4j数据库连接已关闭")
    
    def analyze_labels(self):
        """分析数据库中的所有标签"""
        with self.driver.session() as session:
            # 获取所有标签
            labels_result = session.run("CALL db.labels() YIELD label RETURN label ORDER BY label")
            labels = [record['label'] for record in labels_result]
            
            logging.info("📊 数据库标签分析:")
            logging.info("-" * 50)
            
            label_stats = {}
            for label in labels:
                count_result = session.run(f"MATCH (n:{label}) RETURN count(n) as count")
                count = count_result.single()['count']
                label_stats[label] = count
                
                status = "✅ 有效" if count > 0 else "❌ 空标签"
                logging.info(f"  {label:<15} : {count:>6} 个节点 ({status})")
            
            logging.info("-" * 50)
            return label_stats
    
    def identify_problematic_labels(self):
        """识别有问题的标签"""
        label_stats = self.analyze_labels()
        
        # 定义预期的标签
        expected_labels = {'Company', 'Person', 'Partnership'}
        
        # 识别问题标签
        empty_labels = [label for label, count in label_stats.items() if count == 0]
        unexpected_labels = [label for label in label_stats.keys() if label not in expected_labels and label_stats[label] == 0]
        
        logging.info("\n🔍 问题标签分析:")
        
        if empty_labels:
            logging.info(f"📋 空标签 (0个节点): {empty_labels}")
        else:
            logging.info("✅ 没有发现空标签")
        
        if unexpected_labels:
            logging.info(f"⚠️  意外的空标签: {unexpected_labels}")
        else:
            logging.info("✅ 没有发现意外的空标签")
        
        return {
            'empty_labels': empty_labels,
            'unexpected_labels': unexpected_labels,
            'all_stats': label_stats
        }
    
    def cleanup_empty_labels(self, dry_run=False):
        """清理空标签"""
        analysis = self.identify_problematic_labels()
        empty_labels = analysis['empty_labels']
        
        if not empty_labels:
            logging.info("🎉 没有需要清理的空标签")
            return
        
        if dry_run:
            logging.info("🔍 试运行模式 - 将要删除的空标签:")
            for label in empty_labels:
                logging.info(f"  - {label}")
            return
        
        logging.info(f"🧹 开始清理 {len(empty_labels)} 个空标签...")
        
        with self.driver.session() as session:
            for label in empty_labels:
                try:
                    # Neo4j不能直接删除标签，但我们可以确认这些标签确实没有节点
                    count_result = session.run(f"MATCH (n:{label}) RETURN count(n) as count")
                    count = count_result.single()['count']
                    
                    if count == 0:
                        logging.info(f"  ✅ 确认标签 '{label}' 为空标签")
                    else:
                        logging.warning(f"  ⚠️  标签 '{label}' 实际有 {count} 个节点，跳过删除")
                        
                except Exception as e:
                    logging.error(f"  ❌ 检查标签 '{label}' 时出错: {e}")
        
        logging.info("ℹ️  注意：Neo4j会在重启后自动清理未使用的标签")
    
    def explain_labels(self):
        """解释各个标签的用途"""
        explanations = {
            'Company': '✅ 核心标签 - 企业节点',
            'Person': '✅ 核心标签 - 个人节点', 
            'Partnership': '✅ 核心标签 - 合伙企业节点（Company的子类）',
            'Investment': '❌ 无用标签 - 可能来自旧版本或错误导入',
            'Pledge': '❌ 无用标签 - 可能来自旧版本或错误导入',
            'Shareholding': '❌ 无用标签 - 可能来自旧版本或错误导入'
        }
        
        analysis = self.identify_problematic_labels()
        
        logging.info("\n📚 标签用途说明:")
        logging.info("=" * 60)
        
        for label, count in analysis['all_stats'].items():
            explanation = explanations.get(label, '❓ 未知标签 - 需要进一步调查')
            logging.info(f"{label:<15} ({count:>4} 节点): {explanation}")
        
        logging.info("\n💡 建议:")
        logging.info("- Company/Person/Partnership 是系统的核心标签，应该保留")
        logging.info("- Investment/Pledge/Shareholding 是无用的空标签，可以忽略")
        logging.info("- Neo4j会在重启后自动清理未使用的标签")
        logging.info("- 如果担心，可以备份数据库后重启Neo4j服务")
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        analysis = self.identify_problematic_labels()
        
        total_labels = len(analysis['all_stats'])
        empty_labels_count = len(analysis['empty_labels'])
        active_labels = {k: v for k, v in analysis['all_stats'].items() if v > 0}
        
        report = f"""
标签清理报告
============
总标签数量: {total_labels}
活跃标签数量: {len(active_labels)}
空标签数量: {empty_labels_count}

活跃标签详情:
"""
        for label, count in active_labels.items():
            report += f"- {label}: {count} 个节点\n"
        
        if analysis['empty_labels']:
            report += f"\n空标签列表:\n"
            for label in analysis['empty_labels']:
                report += f"- {label}\n"
        
        report += f"""
清理建议:
1. 空标签会在Neo4j重启后自动清理
2. 当前系统只使用 Company、Person、Partnership 三个标签
3. 其他标签可能来自历史数据或错误导入
4. 建议定期检查标签使用情况
"""
        
        logging.info(report)
        return report

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='清理Neo4j数据库中的无用标签')
    parser.add_argument('--analyze', action='store_true', help='分析标签使用情况')
    parser.add_argument('--explain', action='store_true', help='解释各标签用途')
    parser.add_argument('--cleanup', action='store_true', help='清理空标签')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式')
    parser.add_argument('--report', action='store_true', help='生成详细报告')
    
    args = parser.parse_args()
    
    cleaner = LabelCleaner(NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)
    
    try:
        if args.analyze:
            cleaner.analyze_labels()
        elif args.explain:
            cleaner.explain_labels()
        elif args.cleanup:
            cleaner.cleanup_empty_labels(dry_run=args.dry_run)
        elif args.report:
            cleaner.generate_cleanup_report()
        else:
            # 默认行为：完整分析
            logging.info("🚀 开始标签分析...")
            cleaner.explain_labels()
            
    except Exception as e:
        logging.error(f"❌ 执行过程中发生错误: {e}")
    finally:
        cleaner.close()

if __name__ == "__main__":
    main()

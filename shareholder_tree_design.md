# 天眼查股权树抓取与存储方案设计

## 1. 目标与范围

本项目旨在扩展现有的天眼查爬虫功能，实现对公司股权结构的向上穿透分析。从一个或多个起始公司开始，脚本将递归地抓取其股东信息。如果股东本身也是一家公司，则继续向上追溯，直到达到预设的深度或所有分支的顶端，从而构建出一张完整的股权关系网络（股权树）。

最终目标是将这些结构化的数据存入数据库，以便于后续的分析、可视化或导出。

---

## 2. 核心抓取逻辑 (树形遍历)

我们将把股权结构看作一个有向图，其中每个公司是一个"节点"，每条持股关系是一条"边"。抓取过程实质上是在这个图上进行遍历。

### 2.1. 遍历算法：广度优先搜索 (BFS)

我们将采用广度优先搜索（BFS）算法来管理抓取队列。

- **工作原理**: 从起始公司开始，先抓取其所有一级股东。然后，将这些股东中属于公司的实体加入到一个队列中。完成第一层后，再从队列中取出公司，继续抓取它们的股东（即第二层），以此类推。
- **优势**:
    - **控制简单**: 可以非常容易地通过层级计数来限制抓取深度。
    - **资源友好**: 相对于深度优先（DFS），BFS的抓取路径更发散，可以避免在单一深层分支上花费过长时间，更适合网络抓取。

### 2.2. 核心流程

1.  **初始化**:
    - 创建一个先进先出（FIFO）的 **抓取队列 `queue`**。
    - 创建一个 **已访问集合 `visited_urls`**，用于存储已处理或已在队列中的公司URL，防止重复抓取和循环引用。
    - 将起始公司的URL和初始深度（例如 `depth=0`）作为一个元组 `(url, depth)` 加入 `queue` 和 `visited_urls`。

2.  **循环抓取**:
    - 当 `queue` 不为空时，从队列中取出一个 `(current_url, current_depth)`。
    - **检查深度**: 如果 `current_depth` 超过了预设的 `max_depth`，则跳过，不继续抓取。
    - **执行抓取**: 调用 `tianyancha_scraper.py` 中的 `scrape_company_info` 方法抓取该公司信息。
    - **数据入库**: 将抓取到的公司基本信息和股东信息存入数据库（详见第3节）。
    - **发现新节点**: 遍历该公司的股东列表：
        - 如果一个股东是公司（即拥有指向 `/company/...` 的链接 `shareholder_url`）。
        - 并且 `shareholder_url` **不在 `visited_urls` 集合中**。
        - 则将 `(shareholder_url, current_depth + 1)` 添加到 `queue` 和 `visited_urls` 集合中。

3.  **结束**: 当队列为空时，表示在指定深度内的所有相关公司都已抓取完毕。

### 2.3. 控制参数

- `start_companies`: 一个包含起始公司名称或URL的列表。
- `max_depth`: 一个整数，定义了向上穿透的最大层数。例如 `max_depth=3` 表示最多追溯到太孙公司辈分。

---

## 3. 数据库模型设计 (SQLAlchemy)

为了高效地存储和查询树形/图形结构数据，并自然地处理"节点合并"（即同一家公司在数据库中只存一份），我们设计以下两个核心表。

### 3.1. `companies` 表 (节点)

该表存储公司的基本信息。每个公司实体在这里是唯一的。

- **表结构**:
    - `id` (Integer, Primary Key, Autoincrement): 数据库内部唯一ID。
    - `tianyancha_id` (BigInteger, Unique, Not Null): 天眼查URL中的公司ID (例如 `15964459`)。这是**识别和合并节点的关键字段**。
    - `name` (String, Not Null): 公司名称。
    - `credit_code` (String, Index): 统一社会信用代码。
    - `establishment_date` (Date): 成立日期。
    - `legal_representative` (String): 法定代表人。
    - `registered_capital` (String): 注册资本。
    - `source_url` (String): 原始天眼查URL。
    - `extra_data` (JSON): 用于存储其他不常用的基本信息，如地址、经营范围等，便于扩展。
    - `created_at` / `updated_at`: 时间戳。

### 3.2. `shareholder_relationships` 表 (边)

该表存储股权关系，连接了不同的公司节点。

- **表结构**:
    - `id` (Integer, Primary Key, Autoincrement): 关系ID。
    - `company_id` (Integer, ForeignKey('companies.id')): 被投资公司的ID（子公司）。
    - `shareholder_id` (Integer, ForeignKey('companies.id'), Nullable=True): 股东公司的ID。**如果股东是个人而非公司，此字段为NULL**。
    - `shareholder_name` (String, Not Null): 股东的名称（无论是公司还是个人）。
    - `shareholding_percentage` (String): 持股比例。
    - `investment_amount` (String): 认缴出资额。
    - `extra_data` (JSON): 用于存储其他关系信息，如认缴日期等。

### 3.3. 数据插入逻辑 (处理节点合并)

1.  **抓取到一家公司A后**: 从其URL中提取 `tianyancha_id`。
2.  **查询 `companies` 表**: `SELECT id FROM companies WHERE tianyancha_id = ?`。
3.  **判断**:
    - **若存在**: 说明该公司已入库。获取其 `id`，并根据需要更新其信息（`UPDATE`）。
    - **若不存在**: 说明是新公司。向 `companies` 表中 `INSERT` 一条新纪录，并获取新生成的 `id`。
4.  **抓取到公司A的股东B后**:
    - B是公司：重复步骤1-3，获取B的数据库 `id`。
    - B是个人：`shareholder_id` 设为 `NULL`。
5.  **创建关系**: 向 `shareholder_relationships` 表中 `INSERT` 一条新纪录，包含A的 `id` 和B的 `id` (如果B是公司)。

---

## 4. 建议的项目结构

为了保持代码的模块化和可读性，建议采用以下文件结构：

```
/shareholder-project/
├── tianyancha_scraper.py      # [已有] 核心爬虫，负责抓取单个页面的信息。
├── database_models.py         # [修改] 定义SQLAlchemy的数据模型 (companies, shareholder_relationships)。
├── tree_crawler.py            # [新增] 股权树遍历的调度器，实现BFS算法和数据库交互。
├── run_crawler.py             # [新增] 主程序入口，负责解析命令行参数并启动抓取任务。
├── requirements.txt           # [已有] 项目依赖。
├── companies.to.search.txt    # [已有] 示例输入文件。
└── shareholder_tree_design.md # [本文档] 设计方案。
```

这样的结构将抓取、数据建模和业务逻辑清晰地分离开来，便于未来的功能扩展和维护。 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构系统测试脚本
- 测试数据标准化功能
- 验证数据库存储正确性
- 检查数据一致性
"""

import logging
import json
from tianyancha_scraper import TianyanchaCompanyScraper
from graph_manager import GraphManager
from data_normalizer import DataNormalizer
from config import NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SystemTester:
    """系统测试器"""
    
    def __init__(self):
        self.scraper = TianyanchaCompanyScraper(use_selenium=False, headless=True)
        self.db_manager = GraphManager(NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)
    
    def test_data_normalization(self):
        """测试数据标准化功能"""
        logging.info("🧪 测试数据标准化功能...")
        
        # 模拟原始数据
        raw_basic_info = {
            '企业名称': '测试科技有限公司',
            '经营状态': '存续',
            '法定代表人': '张三',
            '注册资本': '1000万人民币',
            '成立日期': '2020-01-01',
            '统一社会信用代码': '91110000000000000X',
            '企业类型': '有限责任公司',
            '行业': '软件和信息技术服务业',
            '标签': '投资机构|A股(正常上市)',
            '': '',  # 空键
            '无效字段': '-',  # 无效值
            '空值字段': None,  # 空值
        }
        
        source_url = 'https://www.tianyancha.com/company/12345'
        
        # 标准化数据
        normalized = DataNormalizer.normalize_company_data(raw_basic_info, source_url)
        
        # 验证结果
        assert normalized['name'] == '测试科技有限公司'
        assert normalized['business_status'] == '存续'
        assert normalized['tianyancha_id'] == 12345
        assert normalized['is_partnership'] == False
        assert isinstance(normalized['tags'], list)
        assert '投资机构' in normalized['tags']
        
        # 验证无效数据被过滤
        assert '' not in normalized.values()
        assert '-' not in normalized.values()
        assert None not in normalized.values()
        
        logging.info("✅ 数据标准化测试通过")
        return normalized
    
    def test_shareholder_normalization(self):
        """测试股东数据标准化"""
        logging.info("🧪 测试股东数据标准化...")
        
        raw_shareholders = [
            {
                '股东名称': '投资公司A',
                '持股比例': '30.5%',
                '认缴出资额万元': '305万',
                '股东链接': 'https://www.tianyancha.com/company/67890'
            },
            {
                '股东名称': '个人投资者B',
                '持股比例': '20%',
                '认缴出资额万元': '200万',
                # 没有股东链接，表示是个人
            },
            {
                '股东名称': '',  # 空名称，应该被过滤
                '持股比例': '10%',
            }
        ]
        
        normalized = DataNormalizer.normalize_shareholder_data(raw_shareholders)
        
        # 验证结果
        assert len(normalized) == 2  # 空名称的股东被过滤
        
        # 验证公司股东
        company_shareholder = normalized[0]
        assert company_shareholder['shareholder_name'] == '投资公司A'
        assert company_shareholder['shareholder_type'] == 'company'
        assert company_shareholder['percentage'] == 30.5
        assert company_shareholder['shareholder_tianyancha_id'] == 67890
        
        # 验证个人股东
        person_shareholder = normalized[1]
        assert person_shareholder['shareholder_name'] == '个人投资者B'
        assert person_shareholder['shareholder_type'] == 'person'
        assert person_shareholder['percentage'] == 20.0
        
        logging.info("✅ 股东数据标准化测试通过")
        return normalized
    
    def test_database_storage(self):
        """测试数据库存储功能"""
        logging.info("🧪 测试数据库存储功能...")
        
        # 创建测试数据
        test_data = {
            'source_url': 'https://www.tianyancha.com/company/99999',
            'basic_info': {
                '企业名称': '测试存储公司',
                '经营状态': '存续',
                '法定代表人': '李四',
                '注册资本': '500万人民币',
                '企业类型': '有限合伙企业',
                '标签': '投资机构',
                '受益所有人': [
                    {
                        '受益所有人名称': '张三',
                        '最终受益股份': '30%',
                        '受益类型': '实际控制人',
                        '任职类型': '董事长',
                        '判定理由': '持股超过25%'
                    }
                ],
                '实际控制人': [
                    {
                        '实际控制人名称': '李四',
                        '直接持股比例': '15%',
                        '总持股比例': '35%'
                    }
                ]
            },
            'shareholders_info': [
                {
                    '股东名称': '合伙人A',
                    '持股比例': '60%',
                    '股东链接': 'https://www.tianyancha.com/company/88888'
                }
            ],
            'investments_info': [
                {
                    '被投公司名称': '被投资公司X',
                    '投资占比': '25%',
                    '被投公司链接': 'https://www.tianyancha.com/company/77777'
                }
            ]
        }
        
        try:
            # 存储数据
            self.db_manager.save_company_data(test_data)
            
            # 验证数据是否正确存储
            with self.db_manager.driver.session() as session:
                # 检查公司节点
                company_result = session.run("""
                    MATCH (c:Company {tianyancha_id: 99999})
                    RETURN c
                """).single()
                
                assert company_result is not None
                company = company_result['c']
                assert company['name'] == '测试存储公司'
                assert company['is_partnership'] == True
                
                # 检查Partnership标签
                partnership_result = session.run("""
                    MATCH (c:Company:Partnership {tianyancha_id: 99999})
                    RETURN c
                """).single()
                assert partnership_result is not None
                
                # 检查股东关系
                shareholder_result = session.run("""
                    MATCH (s:Company {tianyancha_id: 88888})-[r:HOLDS_SHARE]->(c:Company {tianyancha_id: 99999})
                    RETURN s, r
                """).single()
                assert shareholder_result is not None
                assert shareholder_result['r']['percentage'] == 60.0
                
                # 检查投资关系
                investment_result = session.run("""
                    MATCH (c:Company {tianyancha_id: 99999})-[r:HOLDS_SHARE]->(t:Company {tianyancha_id: 77777})
                    RETURN t, r
                """).single()
                assert investment_result is not None
                assert investment_result['r']['investment_percentage'] == 25.0

                # 检查受益所有人关系
                beneficial_owner_result = session.run("""
                    MATCH (p:Person {name: '张三'})-[r:BENEFICIAL_OWNER_OF]->(c:Company {tianyancha_id: 99999})
                    RETURN p, r
                """).single()
                assert beneficial_owner_result is not None
                assert beneficial_owner_result['r']['final_beneficial_share'] == 30.0

                # 检查实际控制人关系
                actual_controller_result = session.run("""
                    MATCH (p:Person {name: '李四'})-[r:ACTUAL_CONTROLLER_OF]->(c:Company {tianyancha_id: 99999})
                    RETURN p, r
                """).single()
                assert actual_controller_result is not None
                assert actual_controller_result['r']['total_shareholding_ratio'] == 35.0
            
            logging.info("✅ 数据库存储测试通过")
            
        except Exception as e:
            logging.error(f"❌ 数据库存储测试失败: {e}")
            raise
    
    def test_data_consistency(self):
        """测试数据一致性"""
        logging.info("🧪 测试数据一致性...")
        
        with self.db_manager.driver.session() as session:
            # 检查是否有重复的tianyancha_id
            duplicate_result = session.run("""
                MATCH (c:Company)
                WITH c.tianyancha_id as id, count(*) as cnt
                WHERE cnt > 1
                RETURN id, cnt
            """).data()
            
            assert len(duplicate_result) == 0, f"发现重复的tianyancha_id: {duplicate_result}"
            
            # 检查是否有空的企业名称
            empty_name_result = session.run("""
                MATCH (c:Company)
                WHERE c.name IS NULL OR c.name = ''
                RETURN count(c) as count
            """).single()['count']
            
            assert empty_name_result == 0, f"发现 {empty_name_result} 个空名称的公司节点"
            
            # 检查关系的一致性
            invalid_relations = session.run("""
                MATCH (a)-[r:HOLDS_SHARE|BENEFICIAL_OWNER_OF|ACTUAL_CONTROLLER_OF]->(b)
                WHERE a = b
                RETURN count(r) as count
            """).single()['count']
            
            assert invalid_relations == 0, f"发现 {invalid_relations} 个自环关系"
            
            logging.info("✅ 数据一致性测试通过")
    
    def cleanup_test_data(self):
        """清理测试数据"""
        logging.info("🧹 清理测试数据...")
        
        with self.db_manager.driver.session() as session:
            # 删除测试节点和关系
            session.run("""
                MATCH (c:Company)
                WHERE c.tianyancha_id IN [99999, 88888, 77777, 12345, 67890]
                DETACH DELETE c
            """)
            
            session.run("""
                MATCH (p:Person)
                WHERE p.name IN ['个人投资者B', '张三', '李四']
                DELETE p
            """)
        
        logging.info("✅ 测试数据清理完成")
    
    def run_all_tests(self):
        """运行所有测试"""
        logging.info("🚀 开始系统测试...")
        
        try:
            # 1. 测试数据标准化
            self.test_data_normalization()
            self.test_shareholder_normalization()
            
            # 2. 测试数据库存储
            self.test_database_storage()
            
            # 3. 测试数据一致性
            self.test_data_consistency()
            
            logging.info("🎉 所有测试通过！系统重构成功！")
            
        except Exception as e:
            logging.error(f"❌ 测试失败: {e}")
            raise
        finally:
            # 清理测试数据
            self.cleanup_test_data()
            self.scraper.close()
            self.db_manager.close()

def main():
    """主函数"""
    tester = SystemTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()

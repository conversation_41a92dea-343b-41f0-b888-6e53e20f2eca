#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理稀有属性脚本
- 删除记录数小于指定阈值的节点属性键
- 减少数据库中的键值数量，提升查询性能
- 保留核心属性，清理冗余属性
"""

import logging
import argparse
from neo4j import GraphDatabase
from config import NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class RarePropertyCleaner:
    """稀有属性清理器"""
    
    def __init__(self, uri, user, password):
        try:
            self.driver = GraphDatabase.driver(uri, auth=(user, password))
            self.driver.verify_connectivity()
            logging.info(f"✅ 成功连接到Neo4j数据库: {uri}")
        except Exception as e:
            logging.error(f"❌ 无法连接到Neo4j数据库: {e}")
            raise
    
    def close(self):
        if self.driver:
            self.driver.close()
            logging.info("🔒 Neo4j数据库连接已关闭")
    
    def analyze_property_usage(self, node_label=None):
        """
        分析节点属性的使用情况
        Args:
            node_label: 节点标签，如 'Company' 或 'Person'，None表示所有节点
        Returns:
            属性使用统计字典
        """
        with self.driver.session() as session:
            if node_label:
                query = f"""
                MATCH (n:{node_label})
                UNWIND keys(n) as key
                RETURN key, count(*) as usage_count
                ORDER BY usage_count DESC
                """
            else:
                query = """
                MATCH (n)
                UNWIND keys(n) as key
                RETURN key, count(*) as usage_count
                ORDER BY usage_count DESC
                """
            
            result = session.run(query)
            property_stats = {}
            
            for record in result:
                key = record['key']
                count = record['usage_count']
                property_stats[key] = count
            
            return property_stats
    
    def get_rare_properties(self, threshold=10, node_label=None, protected_keys=None):
        """
        获取使用次数少于阈值的属性键
        Args:
            threshold: 使用次数阈值
            node_label: 节点标签
            protected_keys: 受保护的属性键列表（不会被删除）
        Returns:
            稀有属性键列表
        """
        if protected_keys is None:
            protected_keys = [
                'tianyancha_id', 'name', 'created_at', 'updated_at', 'cleaned_at',
                'business_status', 'legal_representative', 'registered_capital',
                'establishment_date', 'credit_code', 'source_url', 'is_partnership'
            ]
        
        property_stats = self.analyze_property_usage(node_label)
        rare_properties = []
        
        for key, count in property_stats.items():
            if count < threshold and key not in protected_keys:
                rare_properties.append((key, count))
        
        return rare_properties
    
    def preview_cleanup(self, threshold=10, node_label=None):
        """
        预览清理操作，显示将要删除的属性
        """
        rare_properties = self.get_rare_properties(threshold, node_label)
        
        if not rare_properties:
            logging.info(f"🎉 没有发现使用次数少于 {threshold} 的稀有属性")
            return []
        
        logging.info(f"📊 发现 {len(rare_properties)} 个稀有属性（使用次数 < {threshold}）:")
        logging.info("-" * 60)
        
        total_records_to_clean = 0
        for key, count in rare_properties:
            logging.info(f"  {key:<30} : {count:>5} 次使用")
            total_records_to_clean += count
        
        logging.info("-" * 60)
        logging.info(f"总计将清理 {total_records_to_clean} 个属性记录")
        
        return rare_properties
    
    def cleanup_rare_properties(self, threshold=10, node_label=None, dry_run=False):
        """
        清理稀有属性
        Args:
            threshold: 使用次数阈值
            node_label: 节点标签
            dry_run: 是否为试运行（不实际删除）
        """
        rare_properties = self.get_rare_properties(threshold, node_label)
        
        if not rare_properties:
            logging.info(f"🎉 没有需要清理的稀有属性")
            return
        
        if dry_run:
            logging.info("🔍 试运行模式 - 不会实际删除属性")
            self.preview_cleanup(threshold, node_label)
            return
        
        logging.info(f"🧹 开始清理 {len(rare_properties)} 个稀有属性...")
        
        with self.driver.session() as session:
            cleaned_count = 0
            
            for key, count in rare_properties:
                try:
                    if node_label:
                        query = f"""
                        MATCH (n:{node_label})
                        WHERE n.{key} IS NOT NULL
                        REMOVE n.{key}
                        RETURN count(n) as removed_count
                        """
                    else:
                        query = f"""
                        MATCH (n)
                        WHERE n.{key} IS NOT NULL
                        REMOVE n.{key}
                        RETURN count(n) as removed_count
                        """
                    
                    result = session.run(query).single()
                    removed_count = result['removed_count'] if result else 0
                    
                    logging.info(f"  ✅ 删除属性 '{key}': {removed_count} 个节点")
                    cleaned_count += removed_count
                    
                except Exception as e:
                    logging.error(f"  ❌ 删除属性 '{key}' 时出错: {e}")
            
            logging.info(f"🎉 清理完成！总共清理了 {cleaned_count} 个属性记录")
    
    def generate_cleanup_report(self, node_label=None):
        """生成清理报告"""
        property_stats = self.analyze_property_usage(node_label)
        
        if not property_stats:
            logging.info("📊 没有找到任何属性")
            return
        
        total_properties = len(property_stats)
        total_records = sum(property_stats.values())
        
        # 统计不同使用频率的属性
        freq_ranges = {
            '1次': 0,
            '2-5次': 0,
            '6-10次': 0,
            '11-50次': 0,
            '51-100次': 0,
            '100+次': 0
        }
        
        for count in property_stats.values():
            if count == 1:
                freq_ranges['1次'] += 1
            elif 2 <= count <= 5:
                freq_ranges['2-5次'] += 1
            elif 6 <= count <= 10:
                freq_ranges['6-10次'] += 1
            elif 11 <= count <= 50:
                freq_ranges['11-50次'] += 1
            elif 51 <= count <= 100:
                freq_ranges['51-100次'] += 1
            else:
                freq_ranges['100+次'] += 1
        
        label_text = f"({node_label} 节点)" if node_label else "(所有节点)"
        
        report = f"""
属性使用情况报告 {label_text}
{'='*50}
总属性数量: {total_properties}
总属性记录数: {total_records}

使用频率分布:
- 仅使用1次: {freq_ranges['1次']} 个属性
- 使用2-5次: {freq_ranges['2-5次']} 个属性
- 使用6-10次: {freq_ranges['6-10次']} 个属性
- 使用11-50次: {freq_ranges['11-50次']} 个属性
- 使用51-100次: {freq_ranges['51-100次']} 个属性
- 使用100+次: {freq_ranges['100+次']} 个属性

最常用的10个属性:
"""
        
        # 显示最常用的属性
        sorted_props = sorted(property_stats.items(), key=lambda x: x[1], reverse=True)
        for i, (key, count) in enumerate(sorted_props[:10]):
            report += f"  {i+1:2d}. {key:<25} : {count:>6} 次\n"
        
        logging.info(report)
        return report

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='清理Neo4j数据库中的稀有属性',
        formatter_class=argparse.RawTextHelpFormatter
    )
    
    parser.add_argument('-t', '--threshold', type=int, default=10,
                       help='使用次数阈值，少于此次数的属性将被删除（默认: 10）')
    
    parser.add_argument('-l', '--label', type=str,
                       help='指定节点标签（如 Company 或 Person），不指定则处理所有节点')
    
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行模式，只显示将要删除的属性，不实际删除')
    
    parser.add_argument('--report-only', action='store_true',
                       help='仅生成属性使用报告，不进行清理')
    
    parser.add_argument('--preview', action='store_true',
                       help='预览将要清理的稀有属性')
    
    args = parser.parse_args()
    
    cleaner = RarePropertyCleaner(NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)
    
    try:
        if args.report_only:
            # 仅生成报告
            logging.info("📊 生成属性使用报告...")
            cleaner.generate_cleanup_report(args.label)
        
        elif args.preview:
            # 预览清理
            logging.info(f"🔍 预览稀有属性（阈值: {args.threshold}）...")
            cleaner.preview_cleanup(args.threshold, args.label)
        
        else:
            # 执行清理
            if args.dry_run:
                logging.info("🔍 试运行模式 - 预览清理操作")
            else:
                logging.info("⚠️  即将执行实际清理操作")
                confirm = input("确认继续吗？(y/N): ")
                if confirm.lower() != 'y':
                    logging.info("❌ 操作已取消")
                    return
            
            cleaner.cleanup_rare_properties(args.threshold, args.label, args.dry_run)
        
    except Exception as e:
        logging.error(f"❌ 执行过程中发生错误: {e}")
    finally:
        cleaner.close()

if __name__ == "__main__":
    main()

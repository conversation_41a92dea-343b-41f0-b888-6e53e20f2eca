#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
天眼查企业信息爬虫 - 统一版本
支持基础爬虫和Selenium浏览器自动化两种模式
"""

import requests
from bs4 import BeautifulSoup
import json
import pandas as pd
import time
import random
import re
import os
import sys
import subprocess
from datetime import datetime
from urllib.parse import urljoin
from enum import Enum, auto

# Selenium相关导入（可选）
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.common.exceptions import TimeoutException, NoSuchElementException
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

class CompanyType(Enum):
    """枚举公司类型"""
    REGULAR = auto()      # 普通公司
    LISTED = auto()       # 上市公司
    PARTNERSHIP = auto()  # 有限合伙/普通合伙

class TianyanchaCompanyScraper:
    """天眼查企业信息爬虫"""
    
    def __init__(self, use_selenium=True, headless=True):
        """
        初始化爬虫
        Args:
            use_selenium: 是否使用Selenium模式，默认True
            headless: 是否在无头模式下运行Selenium，默认True
        """
        self.use_selenium = use_selenium and SELENIUM_AVAILABLE
        self.headless = headless
        self.driver = None
        
        # 初始化requests session
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
        }
        self.session.headers.update(self.headers)
        
        if self.use_selenium:
            print("🚀 使用Selenium模式（可获取完整股东信息）")
        else:
            print("⚡ 使用基础模式（仅企业基本信息）")
            if not SELENIUM_AVAILABLE:
                print("💡 安装Selenium可获取完整功能：pip install selenium webdriver-manager")
    
    def install_selenium_deps(self):
        """自动安装Selenium依赖"""
        try:
            print("正在安装Selenium依赖...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "selenium", "webdriver-manager"])
            print("✅ Selenium依赖安装完成，请重启程序")
            return True
        except Exception as e:
            print(f"❌ 安装失败: {e}")
            return False
    
    def setup_selenium(self):
        """设置Selenium环境"""
        if not SELENIUM_AVAILABLE:
            print("Selenium未安装，尝试自动安装...")
            if self.install_selenium_deps():
                print("请重启程序以使用Selenium功能")
            return False
        
        try:
            # 自动安装ChromeDriver
            print("正在设置ChromeDriver...")
            driver_path = ChromeDriverManager().install()
            
            # 设置Chrome选项
            chrome_options = Options()

            # --- 连接到现有浏览器 ---
            chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
            # --------------------------

            # 以下选项在连接现有浏览器时通常不再需要，但保留以备后用
            # if self.headless:
            #     print("...将在无头模式下运行浏览器...")
            #     chrome_options.add_argument('--headless')
            # 
            # # --- 反爬虫伪装 ---
            # chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            # chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            # chrome_options.add_experimental_option('useAutomationExtension', False)
            # # --------------------
            # 
            # chrome_options.add_argument('--no-sandbox')
            # chrome_options.add_argument('--disable-dev-shm-usage')
            # chrome_options.add_argument('--disable-gpu')
            # chrome_options.add_argument('--window-size=1920,1080')
            # chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # # --- 执行JS脚本，进一步隐藏特征 ---
            # # 连接现有浏览器时，通常也不需要执行这个，因为这是一个真实的人类会话
            # self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            # # ------------------------------------

            self.driver.implicitly_wait(10)
            
            print("✅✅✅ 成功连接到现有浏览器！")
            return True
            
        except Exception as e:
            print(f"❌ Selenium设置失败: {e}")
            print("将回退到基础模式")
            self.use_selenium = False
            return False
    
    def get_page(self, url, max_retries=3):
        """获取网页内容"""
        if self.use_selenium:
            return self._get_page_selenium(url)
        else:
            return self._get_page_requests(url, max_retries)
    
    def _get_page_requests(self, url, max_retries):
        """使用requests获取页面"""
        for attempt in range(max_retries):
            try:
                time.sleep(random.uniform(3, 10))
                print(f"📡 正在获取网页 (第{attempt + 1}次): {url}")
                
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                response.encoding = 'utf-8'
                
                print(f"✅ 成功获取网页，状态码: {response.status_code}")
                return response.text
                
            except Exception as e:
                print(f"❌ 获取失败: {e}")
                if attempt == max_retries - 1:
                    raise e
                time.sleep(random.uniform(3, 10))
    
    def _get_page_selenium(self, url):
        """使用Selenium获取页面"""
        if not self.driver and not self.setup_selenium():
            # Selenium设置失败，回退到requests
            return self._get_page_requests(url, 3)
        
        try:
            print(f"🌐 Selenium访问: {url}")
            self.driver.get(url)

            # 智能等待，根据页面类型决定等待条件
            if "/nsearch" in url:
                # 这是搜索页面，等待搜索结果列表容器出现
                try:
                    print("...正在等待搜索结果加载...")
                    WebDriverWait(self.driver, 15).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "div.index_list-content__wjkNi"))
                    )
                    print("✅ 搜索结果列表已加载")
                    time.sleep(random.uniform(1, 2.5)) # 等待JS渲染稳定
                except Exception:
                    print("⚠️ 搜索结果加载超时！可能是因为出现了验证码。")
                    print("   如果浏览器窗口可见，请尝试手动操作后让程序继续。")
                    self.driver.save_screenshot('screenshot_search_timeout.png')
                    print("   已保存截图 `screenshot_search_timeout.png` 以便分析。")
            
            elif "/company/" in url:
                # 这是公司详情页，等待股东信息出现
                try:
                    WebDriverWait(self.driver, 10).until(
                        lambda driver: "股东名称" in driver.page_source or "股东信息" in driver.page_source
                    )
                    print("✅ 股东信息已加载")
                    time.sleep(random.uniform(1, 2))
                except Exception:
                    print("⚠️ 股东信息加载超时，将继续解析当前页面内容。")
            
            return self.driver.page_source
            
        except Exception as e:
            print(f"❌ Selenium访问失败: {e}")
            raise
    
    def parse_basic_info(self, soup):
        """解析企业基本信息，优先从语义化标签获取，再从表格补充"""
        basic_info = {}
        
        try:
            # --- 阶段一: 从页面头部等关键区域，通过更稳定的选择器提取核心信息 ---
            header_name = ''
            header_status = ''
            
            # 新策略: 直接定位H1标签，它是最核心的标志
            h1_tag = soup.find('h1', class_=re.compile(r'company-name|index_name'))
            
            if h1_tag:
                # 从H1中分别提取名称和状态，避免文本处理的脆弱性
                name_span = h1_tag.find('span', class_=re.compile(r'index_name'))
                if name_span:
                    header_name = name_span.get_text(strip=True)
                else:
                    # 如果没有内层span，则取h1的直接文本作为备用
                    header_name = h1_tag.find(string=True, recursive=False).strip()

                status_tag = h1_tag.find('div', class_='index_reg-status-tag__ES7dF')
                if status_tag:
                    header_status = status_tag.get_text(strip=True)

            # --- 阶段二: 解析常规信息表格，作为补充信息 ---
            info_table = soup.find('table', class_='index_tableBox__ZadJW')
            if info_table:
                rows = info_table.find_all('tr')
                for row in rows:
                    cells = row.find_all('td')
                    if len(cells) >= 2:
                        # 跳过企业名称/法定代表人，因为头部信息更可靠或已有更佳解析
                        first_cell_text = cells[0].get_text(strip=True)
                        if '企业名称' in first_cell_text or '法定代表人' in first_cell_text:
                            continue
                        self._parse_table_cell(cells, basic_info)
            
            # --- 阶段三: 整合与清洗数据 ---
            # 3a. 确定最终的企业名称 (Header优先)
            basic_info['企业名称'] = header_name or basic_info.get('企业名称', '未知公司')

            # 3b. 确定法定代表人 (从表格外专门解析，因为可能含复杂标签)
            legal_rep_td = soup.find('td', text=re.compile(r'^\s*法定代表人\s*$'))
            if legal_rep_td and legal_rep_td.find_next_sibling('td'):
                 rep_link = legal_rep_td.find_next_sibling('td').find('a', class_='link-click')
                 if rep_link:
                     basic_info['法定代表人'] = rep_link.get_text(strip=True)
            
            # 3c. 统一经营状态字段
            # 优先级: Header中的高亮状态 > 表格中的"登记状态"
            table_reg_status = basic_info.get('登记状态')
            final_status = header_status or table_reg_status
            if final_status:
                basic_info['经营状态'] = final_status
            
            # 3d. 清理冗余字段
            redundant_fields = ['登记状态']
            for field in redundant_fields:
                if field in basic_info:
                    del basic_info[field]

            # 3e. 移除空值和无意义的值
            basic_info = {k: v for k, v in basic_info.items()
                         if v is not None and v != '' and v != '-' and v != '暂无'}
        
        except Exception as e:
            print(f"❌ 解析基本信息出错: {e}")
        
        return basic_info
    
    def _parse_table_cell(self, cells, info_dict):
        """解析表格单元格，只保留有效的非空数据"""
        for i in range(0, len(cells) - 1, 2):
            if i + 1 < len(cells):
                title = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', cells[i].get_text(strip=True))
                if title:
                    value_tag = cells[i + 1]
                    value = value_tag.get_text(strip=True)
                    # 只存储非空且有意义的值
                    if value and value != '-' and value != '暂无':
                        info_dict[title] = value

    def parse_shareholders_info(self, soup, company_type=CompanyType.REGULAR):
        """解析股东信息（兼容普通公司 / 上市公司 / 有限合伙企业）"""
        shareholders_info = []
        try:
            shareholder_table = self._find_shareholder_table(soup)
            if not shareholder_table:
                print("⚠️ 未找到股东信息表格")
                if not self.use_selenium:
                    print("💡 建议使用Selenium模式获取完整股东信息")
                return shareholders_info

            thead = shareholder_table.find('thead')
            header_cells = []
            if thead:
                header_cells = [th.get_text(strip=True) for th in thead.find_all('th')]
            # print(f"DEBUG header: {header_cells}")

            tbody = shareholder_table.find('tbody')
            if not tbody:
                print("⚠️ 未找到股东数据")
                return shareholders_info

            rows = tbody.find_all('tr')
            print(f"📊 找到 {len(rows)} 行股东数据")

            for row in rows:
                cells = row.find_all('td')
                if not cells:
                    continue
                shareholder_data = self._parse_shareholder_row(cells, header_cells, company_type)
                if shareholder_data.get('股东名称'):
                    shareholders_info.append(shareholder_data)
                    print(f"✅ 解析股东 {len(shareholders_info)}: {shareholder_data['股东名称']}")
        except Exception as e:
            print(f"❌ 解析股东信息时出错: {e}")

        print(f"📋 总共解析到 {len(shareholders_info)} 个股东")
        return shareholders_info

    def _get_text_from_span_or_cell(self, cell):
        """优先从内部的<span>标签获取文本，否则返回整个单元格的文本。"""
        span_tag = cell.find('span')
        if span_tag:
            return span_tag.get_text(strip=True)
        return cell.get_text(strip=True)

    def _get_link_info_or_text(self, cell, class_name='link-click'):
        """
        优先从内部的<a>标签获取名称和链接，
        否则返回整个单元格的文本和一个空的链接。
        """
        link_tag = cell.find('a', class_=class_name)
        if link_tag:
            return link_tag.get_text(strip=True), link_tag.get('href', '')
        return cell.get_text(strip=True), ''

    def _parse_shareholder_row(self, cells, headers, company_type):
        """
        根据表头和公司类型，智能解析股东信息行。
        针对上市公司和合伙企业做了特殊处理。
        """
        data = {}
        for idx, cell in enumerate(cells):
            header = headers[idx] if idx < len(headers) else ''
            header_clean = re.sub(r'\s+', '', header)
            
            # --- 使用辅助函数进行核心解析 (调整判断顺序，优先匹配更具体的列名) ---
            # 1. 股东/合伙人名称和链接
            if any(key in header_clean for key in ['股东名称', '合伙人名称', '股东/发起人']):
                name, link = self._get_link_info_or_text(cell)
                data['股东名称'] = name
                if link:
                    data['股东链接'] = link
            
            # 2. 间接持股比例 (更具体的判断条件优先)
            elif '间接持股比例' in header_clean:
                data['间接持股比例'] = self._get_text_from_span_or_cell(cell)

            # 3. 持股/出资比例 (主要的比例)
            elif '出资比例' in header_clean or '持股比例' in header_clean:
                data['持股比例'] = self._get_text_from_span_or_cell(cell)

            # 4. 认缴出资额
            elif '认缴出资额' in header_clean:
                data['认缴出资额万元'] = self._get_text_from_span_or_cell(cell)
            
            # 5. 其他常规字段
            else:
                text_content = cell.get_text(strip=True)
                if '序号' in header_clean:
                    data['序号'] = text_content
                elif '认缴出资日期' in header_clean:
                    data['认缴出资日期'] = text_content
                elif '首次持股日期' in header_clean:
                    data['首次持股日期'] = text_content
                elif '关联产品' in header_clean or '关联实体' in header_clean:
                    # 上市公司十大股东可能会有关联产品/实体
                    name, _ = self._get_link_info_or_text(cell)
                    data['关联产品机构'] = name
                elif '股份类型' in header_clean:
                    data['股份类型'] = text_content
                elif '持股数' in header_clean:
                    data['持股数'] = text_content

        return data

    def parse_investment_info(self, driver):
        """
        解析对外投资信息，并处理分页
        Args:
            driver: Selenium的WebDriver实例
        """
        investment_info = []
        print("   🔍 开始解析对外投资信息...")
        
        try:
            # 1. 定位到对外投资板块的容器
            investment_section = driver.find_element(By.CSS_SELECTOR, 'div[data-dim="inverst"]')
            
            page = 1
            while True:
                print(f"      - 正在抓取对外投资第 {page} 页...")
                # 2. 获取当前页面的表格行
                soup = BeautifulSoup(driver.page_source, 'html.parser')
                # 重新在每次页面更新后定位投资板块
                current_investment_section_soup = soup.find('div', attrs={'data-dim': 'inverst'})
                if not current_investment_section_soup:
                    print("      - 未找到投资板块，可能已结束。")
                    break
                
                rows = current_investment_section_soup.find('tbody').find_all('tr')
                
                if not rows:
                    print("      - 未在本页找到投资信息。")

                for row in rows:
                    cells = row.find_all('td')
                    if len(cells) < 5: continue
                    
                    try:
                        company_tag = cells[1].find('a', class_='link-click')
                        if not company_tag:
                            continue

                        invested_company = company_tag.text.strip()
                        company_url = company_tag['href']

                        # 只存储非空且有意义的数据
                        investment_data = {
                            "被投公司名称": invested_company,
                            "被投公司链接": company_url,
                        }

                        # 添加其他字段，但过滤空值
                        if len(cells) > 2 and cells[2].text.strip() and cells[2].text.strip() != '-':
                            investment_data["状态"] = cells[2].text.strip()
                        if len(cells) > 3 and cells[3].text.strip() and cells[3].text.strip() != '-':
                            investment_data["成立日期"] = cells[3].text.strip()
                        if len(cells) > 4 and cells[4].text.strip() and cells[4].text.strip() != '-':
                            investment_data["投资占比"] = cells[4].text.strip()
                        if len(cells) > 5 and cells[5].text.strip() and cells[5].text.strip() != '-':
                            investment_data["注册资本"] = cells[5].text.strip()

                        investment_info.append(investment_data)
                    except Exception as e:
                        print(f"      - 解析投资行信息时出错: {e}")
                
                # 3. 查找并点击下一页
                try:
                    # 定位到分页器
                    pagination_div = investment_section.find_element(By.CSS_SELECTOR, "div.pageWrap")
                    # 找到当前激活的页码
                    active_page_element = pagination_div.find_element(By.CSS_SELECTOR, "div.num.active")
                    # 尝试寻找它的下一个兄弟节点，即下一页按钮
                    next_page_element = active_page_element.find_element(By.XPATH, "./following-sibling::div[contains(@class, 'num')]")
                    
                    # 如果下一个元素是"..."，则再找下一个
                    if '...' in next_page_element.text:
                         next_page_element = next_page_element.find_element(By.XPATH, "./following-sibling::div[contains(@class, 'num')]")

                    # 使用JS点击，避免被页面顶部导航栏等元素遮挡
                    driver.execute_script("arguments[0].click();", next_page_element)
                    
                    # 等待一下，让新数据加载
                    time.sleep(random.uniform(2, 4))
                    page += 1
                except NoSuchElementException:
                    # 如果没有下一个兄弟节点，说明是最后一页
                    print("   ✅ 没有更多投资分页，解析完成。")
                    break
                except Exception as e:
                    print(f"   - 点击下一页时发生错误: {e}")
                    break

        except NoSuchElementException:
            print("   ✅ 未在本页面找到对外投资信息板块。")
            return [] # 没有对外投资信息是正常情况
        except Exception as e:
            print(f"❌ 解析对外投资信息时发生严重错误: {e}")

        print(f"   👍 共抓取到 {len(investment_info)} 条对外投资记录。")
        return investment_info

    def _get_company_type(self, basic_info):
        """根据公司名称和标签判断公司类型"""
        company_name = basic_info.get('企业名称', '')
        tags = basic_info.get('标签', '')
        
        if '有限合伙' in company_name or '普通合伙' in company_name:
            return CompanyType.PARTNERSHIP
        if 'A股' in tags or '港股' in tags:
            return CompanyType.LISTED
        
        return CompanyType.REGULAR

    def parse_company_tags(self, soup):
        """解析企业标签列表，返回一个字符串列表"""
        tags = []
        try:
            # 标签容器示例: <div class="index_tag-list-content__E8sLp"> ... </div>
            tag_container = soup.find('div', class_='index_tag-list-content__E8sLp')
            if not tag_container:
                return tags
            # 每个标签 div 都带有类名 index_company-tag__ 前缀，采用模糊匹配更稳健
            for tag_div in tag_container.find_all('div'):
                classes = tag_div.get('class', [])
                if any(cls.startswith('index_company-tag') for cls in classes):
                    text = tag_div.get_text(strip=True)
                    if text:
                        tags.append(text)
        except Exception as e:
            print(f"⚠️ 解析企业标签时出错: {e}")
        return tags

    def scrape_company_info(self, company_url, direction='up'):
        """
        抓取单个公司的完整信息，但根据direction参数决定解析范围
        """
        data = {
            'url': company_url,
            'source_url': company_url,  # 兼容 GraphManager 对 source_url 的依赖
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'basic_info': {},
            'shareholders_info': [],
            'investments_info': []
        }

        if self.use_selenium:
            # --- Selenium 模式 ---
            if not self.driver and not self.setup_selenium():
                print("❌ Selenium 设置失败，无法继续抓取。")
                return None
            
            try:
                print(f"🌐 Selenium 访问: {company_url}")
                self.driver.get(company_url)

                # 等待"股东信息"模块出现，这是一个更可靠的页面加载成功标志
                WebDriverWait(self.driver, 20).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 'div[data-dim="holder"]'))
                )
                print("✅ 公司详情页核心内容已加载")
                time.sleep(random.uniform(1.5, 3)) # 等待JS渲染

                html_content = self.driver.page_source
                soup = BeautifulSoup(html_content, 'html.parser')

                # 1. 解析基础信息 (总是需要)
                data['basic_info'] = self.parse_basic_info(soup)
                # 新增: 解析企业标签
                tags_list = self.parse_company_tags(soup)
                if tags_list:
                    data['basic_info']['标签'] = '|'.join(tags_list)
                
                # 新增：判断公司类型
                company_type = self._get_company_type(data['basic_info'])
                data['company_type'] = company_type.name # 存入data供调试
                print(f"🏢 公司类型识别为: {company_type.name}")

                # 2. 根据方向，选择性地解析关系信息
                if direction in ['up', 'both']:
                    data['shareholders_info'] = self.parse_shareholders_info(soup, company_type)
                
                if direction in ['down', 'both']:
                    data['investments_info'] = self.parse_investment_info(self.driver)

            except TimeoutException:
                print("❌ 页面加载超时！可能是因为出现了验证码或网络问题。")
                screenshot_path = f"screenshot_timeout_{int(time.time())}.png"
                self.driver.save_screenshot(screenshot_path)
                print(f"   已保存截图 `{screenshot_path}` 以便分析。")
                return None
            except Exception as e:
                print(f"❌ 使用Selenium抓取时发生未知错误: {e}")
                return None

        else:
            # --- 基础 Requests 模式 ---
            try:
                html_content = self.get_page(company_url)
                soup = BeautifulSoup(html_content, 'html.parser')
                data['basic_info'] = self.parse_basic_info(soup)
                # 基础模式下同样尝试解析标签
                tags_list = self.parse_company_tags(soup)
                if tags_list:
                    data['basic_info']['标签'] = '|'.join(tags_list)
                
                # 基础模式下判断公司类型
                company_type = self._get_company_type(data['basic_info'])
                data['company_type'] = company_type.name
                print(f"🏢 公司类型识别为: {company_type.name}")

                # 基础模式无法保证获取到股东信息，但仍可尝试
                data['shareholders_info'] = self.parse_shareholders_info(soup, company_type)
                print("⚠️ 警告: 基础模式无法获取对外投资信息。")
            except Exception as e:
                print(f"❌ 使用基础模式抓取时发生错误: {e}")
                return None

        if not data.get('basic_info', {}).get('企业名称'):
             # 尝试从soup中至少获取一个公司名
            try:
                name_tag = soup.find('h1', class_='index_name__L205t')
                if name_tag:
                     data['basic_info']['企业名称'] = name_tag.text.strip()
            except:
                pass # 忽略错误

        return data

    def save_to_files(self, data, filename_prefix="company"):
        """保存数据到文件"""
        try:
            # JSON文件
            json_file = f"{filename_prefix}_data.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"💾 JSON已保存: {json_file}")
            
            # 基本信息CSV
            if data.get('basic_info'):
                basic_csv = f"{filename_prefix}_basic_info.csv"
                pd.DataFrame([data['basic_info']]).to_csv(basic_csv, index=False, encoding='utf-8-sig')
                print(f"📊 基本信息CSV已保存: {basic_csv}")
            
            # 股东信息CSV
            if data.get('shareholders_info'):
                shareholders_csv = f"{filename_prefix}_shareholders.csv"
                pd.DataFrame(data['shareholders_info']).to_csv(shareholders_csv, index=False, encoding='utf-8-sig')
                print(f"👥 股东信息CSV已保存: {shareholders_csv}")
            
            # 投资信息CSV
            if data.get('investments_info'):
                investments_csv = f"{filename_prefix}_investments.csv"
                pd.DataFrame(data['investments_info']).to_csv(investments_csv, index=False, encoding='utf-8-sig')
                print(f"💼 投资信息CSV已保存: {investments_csv}")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False
    
    def close(self):
        """关闭资源"""
        if self.driver:
            self.driver.quit()
            print("🔒 浏览器已关闭")

    def search_and_get_first_company(self, company_name):
        """
        根据公司名称搜索，并返回第一个结果的公司名称和链接
        Args:
            company_name: 要搜索的公司名称
        Returns:
            一个包含 'name' 和 'url' 的字典，如果找不到则返回 None
        """
        print(f"🔍 正在搜索公司: {company_name}")
        search_url = f"https://www.tianyancha.com/nsearch?key={company_name}"
        
        try:
            # 修改: 不再强制使用requests模式，而是遵循爬虫的初始化设置
            # 这确保了如果用户选择Selenium，搜索也会使用Selenium
            html_content = self.get_page(search_url)
            
            if not html_content:
                return None

            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 根据用户提供的HTML结构定位第一个结果
            # a 标签 class="index_alink__zcia5 link-click"
            # 包含在 div class="index_name__qEdWi" 中
            first_result_link = soup.select_one('div.index_search-box__7YVh6 a.index_alink__zcia5')

            if first_result_link:
                company_url = first_result_link.get('href')
                # 确保URL是完整的
                if company_url and not company_url.startswith('http'):
                    company_url = urljoin('https://www.tianyancha.com', company_url)

                company_title_element = first_result_link.find('span')
                company_title = company_title_element.get_text(strip=True) if company_title_element else "未知公司名称"
                
                print(f"✅ 找到结果: {company_title} -> {company_url}")
                return {'name': company_title, 'url': company_url}
            else:
                print(f"⚠️ 未能为 '{company_name}' 找到任何搜索结果。")
                return None

        except Exception as e:
            print(f"❌ 搜索公司 '{company_name}' 时出错: {e}")
            return None

    def _find_shareholder_table(self, soup):
        """在页面中定位包含股东/合伙人信息的表格"""
        for table in soup.find_all('table'):
            thead = table.find('thead')
            if not thead:
                continue
            header_text = thead.get_text()
            if ('股东' in header_text or '合伙人' in header_text) and (
                '持股' in header_text or '出资比例' in header_text):
                return table
        return None

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='天眼查企业信息爬虫')
    # 修改--url为可选参数，并添加帮助信息
    parser.add_argument('--url', type=str, 
                       help='需要爬取的单个企业天眼查URL')
    # 新增参数以接收公司名称列表
    parser.add_argument('--names', type=str, nargs='+', 
                        help='一个或多个要查询的公司名称 (例如: --names "公司A" "公司B")')
    parser.add_argument('--file', type=str, 
                        help='包含公司名称列表的文本文件路径（每行一个公司名）')

    parser.add_argument('--no-selenium', action='store_true', 
                       help='禁用Selenium，仅获取基本信息')
    # 修改输出参数，使其更通用
    parser.add_argument('--output', type=str, default="tianyancha_output", 
                       help='输出文件的前缀 (例如: --output my_results)')
    
    args = parser.parse_args()

    # 收集需要查询的公司名称
    company_names_to_search = []
    if args.names:
        company_names_to_search.extend(args.names)
    if args.file:
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip()]
                company_names_to_search.extend(lines)
            print(f"📂 从文件 '{args.file}' 加载了 {len(lines)} 个公司名称。")
        except FileNotFoundError:
            print(f"❌ 文件未找到: {args.file}")
            return
            
    if not args.url and not company_names_to_search:
        print("❌ 请至少提供一个参数: --url, --names, 或 --file。")
        parser.print_help()
        return

    # 创建爬虫实例
    scraper = TianyanchaCompanyScraper(use_selenium=not args.no_selenium)
    
    try:
        # 场景一: 处理单个给定的URL
        if args.url:
            print(f"🚀 开始处理单个URL: {args.url}")
            result = scraper.scrape_company_info(args.url)
            if result:
                company_name = result.get('basic_info', {}).get('企业名称', 'unknown_company')
                safe_name = re.sub(r'[\\/*?:"<>|]', "", company_name)
                output_prefix = f"{args.output}_{safe_name}"
                
                scraper.save_to_files(result, output_prefix)
                print(f"\n🎉 爬取完成！数据已保存为 {output_prefix}_* 文件。")
        
        # 场景二: 处理公司名称列表
        if company_names_to_search:
            total = len(company_names_to_search)
            print(f"\n🚀 开始处理 {total} 个公司的查询任务...")
            for i, company_name in enumerate(company_names_to_search):
                print("-" * 60)
                print(f"🔄 处理中 ({i+1}/{total}): {company_name}")
                
                search_result = scraper.search_and_get_first_company(company_name)
                
                if search_result and search_result.get('url'):
                    # 获取到URL后，继续爬取该公司的详细信息
                    detailed_info = scraper.scrape_company_info(search_result['url'])
                    if detailed_info:
                        # 使用爬取到的准确公司名作为文件名前缀，更可靠
                        actual_name = detailed_info.get('basic_info', {}).get('企业名称', company_name)
                        safe_name = re.sub(r'[\\/*?:"<>|]', "", actual_name) # 清理文件名中的非法字符
                        output_prefix = f"{args.output}_{safe_name}"
                        
                        scraper.save_to_files(detailed_info, output_prefix)
                        print(f"\n✅ {actual_name} 的信息已成功保存，文件前缀: {output_prefix}")
                else:
                    print(f"⏭️ 未能处理 '{company_name}'，已跳过。")
                
                # 在两次请求之间随机暂停，模拟人类行为
                if i < total - 1:
                    sleep_time = random.uniform(3, 6)
                    print(f"⏳ 暂停 {sleep_time:.2f} 秒...")
                    time.sleep(sleep_time)

    except Exception as e:
        print(f"\n💥 程序发生未预料的错误: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速清理稀有属性脚本
- 简化版本，快速删除记录数小于10的节点属性键
- 专注于Company节点的清理
"""

import logging
from neo4j import GraphDatabase
from config import NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def quick_cleanup_rare_properties(threshold=10):
    """
    快速清理稀有属性
    Args:
        threshold: 使用次数阈值，默认10
    """
    driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
    
    try:
        with driver.session() as session:
            logging.info("🔍 分析Company节点的属性使用情况...")
            
            # 1. 获取所有Company节点的属性使用统计
            property_stats = session.run("""
                MATCH (c:Company)
                UNWIND keys(c) as key
                RETURN key, count(*) as usage_count
                ORDER BY usage_count ASC
            """).data()
            
            # 2. 定义受保护的核心属性（不会被删除）
            protected_keys = {
                'tianyancha_id', 'name', 'created_at', 'updated_at', 'cleaned_at',
                'business_status', 'legal_representative', 'registered_capital',
                'establishment_date', 'credit_code', 'source_url', 'is_partnership',
                'company_type', 'industry', 'business_scope', 'tags'
            }
            
            # 3. 找出稀有属性
            rare_properties = []
            for record in property_stats:
                key = record['key']
                count = record['usage_count']
                if count < threshold and key not in protected_keys:
                    rare_properties.append((key, count))
            
            if not rare_properties:
                logging.info(f"🎉 没有发现使用次数少于 {threshold} 的稀有属性")
                return
            
            logging.info(f"📊 发现 {len(rare_properties)} 个稀有属性:")
            total_to_clean = 0
            for key, count in rare_properties:
                logging.info(f"  - {key}: {count} 次使用")
                total_to_clean += count
            
            logging.info(f"总计将清理 {total_to_clean} 个属性记录")
            
            # 4. 确认清理
            confirm = input("\n确认删除这些稀有属性吗？(y/N): ")
            if confirm.lower() != 'y':
                logging.info("❌ 操作已取消")
                return
            
            # 5. 执行清理
            logging.info("🧹 开始清理稀有属性...")
            cleaned_total = 0
            
            for key, expected_count in rare_properties:
                try:
                    # 删除指定属性
                    result = session.run(f"""
                        MATCH (c:Company)
                        WHERE c.{key} IS NOT NULL
                        REMOVE c.{key}
                        RETURN count(c) as removed_count
                    """).single()
                    
                    removed_count = result['removed_count'] if result else 0
                    cleaned_total += removed_count
                    
                    logging.info(f"  ✅ 删除 '{key}': {removed_count} 个节点")
                    
                except Exception as e:
                    logging.error(f"  ❌ 删除 '{key}' 时出错: {e}")
            
            logging.info(f"🎉 清理完成！总共清理了 {cleaned_total} 个属性记录")
            
            # 6. 生成清理后的统计
            logging.info("\n📊 清理后的属性统计:")
            remaining_stats = session.run("""
                MATCH (c:Company)
                UNWIND keys(c) as key
                RETURN key, count(*) as usage_count
                ORDER BY usage_count DESC
                LIMIT 20
            """).data()
            
            for record in remaining_stats:
                key = record['key']
                count = record['usage_count']
                logging.info(f"  {key}: {count} 次使用")
    
    except Exception as e:
        logging.error(f"❌ 清理过程中发生错误: {e}")
    finally:
        driver.close()
        logging.info("🔒 数据库连接已关闭")

def show_property_stats():
    """显示属性使用统计"""
    driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
    
    try:
        with driver.session() as session:
            logging.info("📊 Company节点属性使用统计:")
            
            stats = session.run("""
                MATCH (c:Company)
                UNWIND keys(c) as key
                RETURN key, count(*) as usage_count
                ORDER BY usage_count DESC
            """).data()
            
            total_properties = len(stats)
            total_records = sum(record['usage_count'] for record in stats)
            
            logging.info(f"总属性数量: {total_properties}")
            logging.info(f"总属性记录数: {total_records}")
            logging.info("\n属性使用排行:")
            
            for i, record in enumerate(stats, 1):
                key = record['key']
                count = record['usage_count']
                percentage = (count / total_records) * 100
                logging.info(f"  {i:2d}. {key:<25} : {count:>6} 次 ({percentage:5.1f}%)")
    
    except Exception as e:
        logging.error(f"❌ 获取统计信息时发生错误: {e}")
    finally:
        driver.close()

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'stats':
            show_property_stats()
            return
        elif sys.argv[1] == 'help':
            print("""
使用方法:
  python quick_cleanup_properties.py        # 清理稀有属性（阈值=10）
  python quick_cleanup_properties.py stats  # 显示属性统计
  python quick_cleanup_properties.py help   # 显示帮助
            """)
            return
    
    # 默认执行清理
    logging.info("🚀 开始快速清理稀有属性...")
    quick_cleanup_rare_properties(threshold=10)

if __name__ == "__main__":
    main()

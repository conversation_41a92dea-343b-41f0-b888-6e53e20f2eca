#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股权知识图谱分析器
- 重点集群发现 (社区检测)
- 重要节点定位 (中心性分析)
- 集群特征分析
- 对外投资关键词云
- 额外分析功能: 股权穿透路径、循环持股检测
"""
import logging
import pandas as pd
import jieba
from wordcloud import WordCloud
import matplotlib.pyplot as plt
import networkx as nx
from graph_manager import GraphManager
from config import NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD
import base64
from io import BytesIO
from datetime import datetime
import os

# --- 全局配置 ---
# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# 配置Matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 'SimHei' 是黑体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号'-'显示为方块的问题
# 中文字体路径 (根据您的系统选择或修改)
# Windows: 'C:/Windows/Fonts/msyh.ttc' (微软雅黑)
# MacOS: '/System/Library/Fonts/PingFang.ttc'
# Linux: '/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc'
FONT_PATH = 'C:/Windows/Fonts/msyh.ttc'
STOPWORDS_PATH = 'stopwords.txt'

class GraphAnalyzer:
    """
    负责执行知识图谱的深度分析
    """
    def __init__(self, uri, user, password):
        """
        初始化分析器并连接到数据库
        """
        self.graph_manager = GraphManager(uri, user, password)
        self.driver = self.graph_manager.driver
        self.stopwords = self._load_stopwords()
        # 检查GDS插件是否存在
        try:
            self._run_query("RETURN gds.version()")
            logging.info("✅ Neo4j Graph Data Science (GDS) library is available.")
        except Exception:
            logging.error("❌ Neo4j Graph Data Science (GDS) library not found.")
            logging.error("许多分析功能将无法使用，请在Neo4j中安装GDS插件。")
            raise

    def _load_stopwords(self):
        """从文件加载停用词."""
        if not os.path.exists(STOPWORDS_PATH):
            logging.warning(f"停用词文件未找到: {STOPWORDS_PATH}，将使用一个空列表。")
            return set()
        with open(STOPWORDS_PATH, 'r', encoding='utf-8') as f:
            stopwords = {line.strip() for line in f}
        logging.info(f"✅ 从 {STOPWORDS_PATH} 加载了 {len(stopwords)} 个停用词。")
        return stopwords

    def close(self):
        """关闭数据库连接"""
        self.graph_manager.close()

    def _run_query(self, query, params=None):
        """
        一个通用的Cypher查询执行器，返回Pandas DataFrame
        """
        with self.driver.session() as session:
            result = session.run(query, params)
            return pd.DataFrame([r.data() for r in result])

    def _df_to_html(self, df):
        """一个辅助函数，将DataFrame转换为美化的HTML表格"""
        if df is None or not isinstance(df, pd.DataFrame) or df.empty:
            return "<p>无可用数据。</p>"
        # 为了美观，限制列表中显示的成员数量
        if '成员' in df.columns:
            df = df.copy()
            df['成员'] = df['成员'].apply(lambda x: x[:5] + ['...'] if isinstance(x, list) and len(x) > 5 else x)
        
        return df.to_html(index=False, classes='table table-striped table-hover', border=0)


    def _generate_horizontal_barchart(self, df, x_col, y_col, title, xlabel, ylabel, color='#3498db'):
        """
        一个通用的水平条形图生成器，返回base64编码的图片
        """
        if df.empty:
            return None
        try:
            plt.figure(figsize=(12, 8))
            # 反转DataFrame使得排名第一的在最上面
            df_sorted = df.iloc[::-1]
            bars = plt.barh(df_sorted[y_col], df_sorted[x_col], color=color)
            plt.xlabel(xlabel, fontsize=12)
            plt.ylabel(ylabel, fontsize=12)
            plt.title(title, fontsize=16, pad=20)
            plt.xticks(rotation=45)
            plt.grid(axis='x', linestyle='--', alpha=0.6)
            # 在条形图上显示数值
            for bar in bars:
                width = bar.get_width()
                plt.text(width, bar.get_y() + bar.get_height()/2, f'{width:.3f}', va='center', ha='left', fontsize=10)
            
            plt.tight_layout()
            
            buffer = BytesIO()
            plt.savefig(buffer, format='png')
            buffer.seek(0)
            img_str = base64.b64encode(buffer.getvalue()).decode('utf-8')
            plt.close()
            return img_str
        except Exception as e:
            logging.error(f"生成条形图时发生错误: {e}")
            return None

    def find_important_nodes(self, top_n=10, algorithm='PageRank'):
        """
        定位图中的重要节点 (公司)，已考虑持股比例作为权重
        :param top_n: 返回前N个最重要的节点
        :param algorithm: 'PageRank' 或 'Degree'
        :return: 一个包含重要节点信息的DataFrame
        """
        logging.info(f"🚀 开始使用加权的 {algorithm} 算法计算节点重要性...")
        
        graph_name = 'company_graph_weighted'
        query_create_graph = f"""
        CALL gds.graph.project.cypher(
            '{graph_name}',
            'MATCH (n) WHERE n:Company OR n:Person RETURN id(n) AS id, labels(n) AS labels',
            'MATCH (s)-[r:HOLDS_SHARE]->(t) WHERE r.percentage IS NOT NULL
             RETURN id(s) AS source, id(t) AS target, COALESCE(toFloat(replace(r.percentage, "%", "")), 0.0) AS weight',
            {{ validateRelationships: false }}
        ) YIELD graphName, nodeCount, relationshipCount
        """
        self._run_query(query_create_graph)

        algo_params = "{ relationshipWeightProperty: 'weight' }"
        if algorithm == 'PageRank':
            query_algo = f"""
            CALL gds.pageRank.stream('{graph_name}', {algo_params})
            YIELD nodeId, score
            RETURN gds.util.asNode(nodeId).name AS name, gds.util.asNode(nodeId).tianyancha_id AS tianyancha_id, score
            ORDER BY score DESC LIMIT {top_n}
            """
        elif algorithm == 'Degree':
            query_algo = f"""
            CALL gds.degree.stream('{graph_name}', {algo_params})
            YIELD nodeId, score
            RETURN gds.util.asNode(nodeId).name AS name, gds.util.asNode(nodeId).tianyancha_id AS tianyancha_id, score
            ORDER BY score DESC LIMIT {top_n}
            """
        else:
            self._run_query(f"CALL gds.graph.drop('{graph_name}') YIELD graphName;")
            raise ValueError("算法必须是 'PageRank' 或 'Degree'")
            
        df = self._run_query(query_algo)
        
        self._run_query(f"CALL gds.graph.drop('{graph_name}') YIELD graphName;")
        logging.info("✅ 重要节点分析完成。")
        return df

    def find_communities(self, algorithm='Louvain'):
        """
        使用社区发现算法识别公司集群，已考虑持股比例作为权重
        :param algorithm: 'Louvain' 或 'WCC'
        :return: 包含节点和其社区ID的DataFrame
        """
        logging.info(f"🚀 开始使用加权的 {algorithm} 算法进行社区发现...")
        
        graph_name = 'company_community_graph_weighted'
        query_create_graph = f"""
        CALL gds.graph.project.cypher(
            '{graph_name}',
            'MATCH (n:Company) RETURN id(n) AS id',
            'MATCH (s:Company)-[r:HOLDS_SHARE]->(t:Company) 
             WHERE r.percentage IS NOT NULL
             RETURN id(s) AS source, id(t) AS target, COALESCE(toFloat(replace(r.percentage, "%", "")), 0.0) AS weight'
        ) YIELD graphName, nodeCount, relationshipCount
        """
        self._run_query(query_create_graph)
        
        query_algo = f"""
        CALL gds.{algorithm.lower()}.stream('{graph_name}', {{ relationshipWeightProperty: 'weight' }})
        YIELD nodeId, communityId
        RETURN gds.util.asNode(nodeId).name AS name, communityId
        """
        df = self._run_query(query_algo)
        
        self._run_query(f"CALL gds.graph.drop('{graph_name}') YIELD graphName;")
        logging.info("✅ 社区发现完成。")
        return df

    def analyze_centrality_for_community(self, community_id, top_n=10, algorithm='PageRank'):
        """
        为特定的社区计算节点中心性
        :param community_id: 目标社区的ID
        :param top_n: 返回前N个最重要的节点
        :param algorithm: 'PageRank' 或 'Degree'
        :return: 一个包含重要节点信息的DataFrame
        """
        logging.info(f"🚀 开始为社区 {community_id} 使用加权的 {algorithm} 算法计算节点重要性...")

        graph_name = f'community_{community_id}_graph'
        
        # 创建一个只包含特定社区节点的子图
        query_create_graph = f"""
        CALL gds.graph.project.cypher(
            '{graph_name}',
            'MATCH (n) WHERE n.communityId = {community_id} RETURN id(n) AS id, labels(n) AS labels',
            'MATCH (s)-[r:HOLDS_SHARE]->(t) 
             WHERE s.communityId = {community_id} AND t.communityId = {community_id} AND r.percentage IS NOT NULL
             RETURN id(s) AS source, id(t) AS target, COALESCE(toFloat(replace(r.percentage, "%", "")), 0.0) AS weight',
            {{ validateRelationships: false }}
        ) YIELD graphName
        """
        self._run_query(query_create_graph)

        algo_params = "{ relationshipWeightProperty: 'weight' }"
        if algorithm == 'PageRank':
            query_algo = f"""
            CALL gds.pageRank.stream('{graph_name}', {algo_params})
            YIELD nodeId, score
            RETURN gds.util.asNode(nodeId).name AS name, score
            ORDER BY score DESC LIMIT {top_n}
            """
        elif algorithm == 'Degree':
            query_algo = f"""
            CALL gds.degree.stream('{graph_name}', {algo_params})
            YIELD nodeId, score
            RETURN gds.util.asNode(nodeId).name AS name, score
            ORDER BY score DESC LIMIT {top_n}
            """
        else:
            self._run_query(f"CALL gds.graph.drop('{graph_name}') YIELD graphName;")
            raise ValueError("算法必须是 'PageRank' 或 'Degree'")
            
        df = self._run_query(query_algo)
        
        # 删除临时图
        self._run_query(f"CALL gds.graph.drop('{graph_name}') YIELD graphName;")
        logging.info(f"✅ 社区 {community_id} 的重要节点分析完成。")
        return df

    def visualize_community_graph(self, community_id, top_n_nodes=50):
        """
        可视化指定社区的图结构
        :param community_id: 社区ID
        :param top_n_nodes: 为避免过于拥挤，限制显示的核心节点数量
        :return: base64编码的图片字符串
        """
        logging.info(f"🎨 正在为社区 {community_id} 生成网络拓扑图...")
        
        # 1. 获取社区内的节点和关系
        query = """
        MATCH (s)-[r:HOLDS_SHARE]->(t)
        WHERE s.communityId = $community_id AND t.communityId = $community_id
        RETURN s.name AS source, t.name AS target, r.percentage AS weight
        """
        df = self._run_query(query, {'community_id': community_id})

        if df.empty:
            logging.warning(f"社区 {community_id} 内部没有发现股权关系，无法绘图。")
            return None

        # 清洗权重数据：去除百分号并转换为浮点数
        try:
            df['weight_float'] = df['weight'].str.replace('%', '', regex=False).astype(float)
        except (AttributeError, ValueError) as e:
            logging.error(f"无法将社区 {community_id} 的权重列转换为数值: {e}，将忽略权重。")
            df['weight_float'] = 1.0 # 如果转换失败，则给一个默认权重

        # 2. 使用NetworkX创建图
        G = nx.from_pandas_edgelist(df, 'source', 'target', edge_attr=['weight', 'weight_float'], create_using=nx.DiGraph())

        # 3. 为避免图片过于拥挤，可以只显示中心性最高的N个节点及其关系
        if len(G.nodes) > top_n_nodes:
            try:
                # 使用PageRank来找出最重要的节点
                pr = nx.pagerank(G, alpha=0.85, weight='weight_float')
                top_nodes = sorted(pr, key=pr.get, reverse=True)[:top_n_nodes]
                G = G.subgraph(top_nodes)
            except Exception as e:
                 logging.warning(f"PageRank计算失败({e})，将随机选择节点进行显示。")
                 # 如果PageRank失败（例如图不收敛），则随机选择节点
                 top_nodes = list(G.nodes())[:top_n_nodes]
                 G = G.subgraph(top_nodes)


        if not G.nodes:
            logging.warning(f"为社区 {community_id} 创建子图后没有节点，无法绘图。")
            return None

        # 4. 绘图
        try:
            plt.figure(figsize=(20, 20))
            pos = nx.spring_layout(G, k=0.8, iterations=50, seed=42)
            
            # 绘制节点
            node_sizes = [len(node) * 100 for node in G.nodes()]
            nx.draw_networkx_nodes(G, pos, node_color='#4E84C4', node_size=node_sizes, alpha=0.9)
            
            # 绘制边
            nx.draw_networkx_edges(G, pos, arrowstyle='->', arrowsize=15, edge_color='gray', alpha=0.5, width=1.5)
            
            # 绘制标签
            nx.draw_networkx_labels(G, pos, font_size=12, font_family='SimHei', font_color='black')
            
            plt.title(f"社区 {community_id} 核心网络拓扑图 (Top {len(G.nodes())} 节点)", fontsize=24)
            plt.axis('off')
            plt.tight_layout()
            
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150)
            buffer.seek(0)
            img_str = base64.b64encode(buffer.getvalue()).decode('utf-8')
            plt.close()
            
            logging.info(f"✅ 社区 {community_id} 拓扑图生成完毕。")
            return img_str
        except Exception as e:
            logging.error(f"为社区 {community_id} 绘图时发生错误: {e}")
            plt.close()
            return None


    def find_community_for_node(self, company_name, communities_df):
        """
        查找包含特定公司的社区及其所有成员
        :param company_name: 目标公司名称
        :param communities_df: 来自 find_communities 的DataFrame
        :return: 包含该社区所有成员的DataFrame，如果没有找到则返回None
        """
        if communities_df.empty or company_name not in communities_df['name'].values:
            logging.warning(f"无法在社区数据中找到公司: {company_name}")
            return None
        
        try:
            community_id = communities_df.loc[communities_df['name'] == company_name, 'communityId'].iloc[0]
            community_members = communities_df[communities_df['communityId'] == community_id]
            
            logging.info(f"公司 '{company_name}' 属于社区 {community_id}，该社区共有 {len(community_members)} 个成员。")
            return community_members
        except IndexError:
            logging.warning(f"无法为公司 '{company_name}' 定位社区。")
            return None

    def analyze_community_characteristics(self, communities_df):
        """
        分析每个社区的特征
        :param communities_df: 来自 find_communities 的DataFrame
        :return: 每个社区的特征摘要DataFrame
        """
        if communities_df.empty:
            logging.warning("社区数据为空，无法进行分析。")
            return pd.DataFrame()

        logging.info("📊 开始分析社区特征...")
        community_stats = communities_df.groupby('communityId')['name'].agg(['count', list]).reset_index()
        community_stats.rename(columns={'count': '节点数', 'list': '成员'}, inplace=True)
        community_stats = community_stats.sort_values(by='节点数', ascending=False)
        # 添加社群标签，方便绘图
        community_stats['community_label'] = '社群 ' + community_stats['communityId'].astype(str)
        logging.info("✅ 社区特征分析完成。")
        return community_stats

    def generate_community_wordcloud(self, community_id, font_path=FONT_PATH):
        """
        为指定社区生成关键词词云 (数据源: 企业名称 + 经营范围)
        :param community_id: 目标社区ID
        :param font_path: 用于显示中文的字体文件路径.
        :return: (status, data)元组. status可以是 'success', 'no_data', 'error'. data是base64图片或错误信息.
        """
        logging.info(f"☁️ 正在为社区 {community_id} 生成经营范围词云...")
        query = """
        MATCH (c:Company)
        WHERE c.communityId = $community_id
        RETURN c.name AS company_name, c.经营范围 as business_scope
        """
        df = self._run_query(query, {'community_id': community_id})
        
        if df.empty:
            logging.warning(f"社区 {community_id} 中未找到任何公司信息。")
            return 'no_data', None

        # 结合公司名和经营范围作为词云文本源
        df['text_source'] = df['company_name'].fillna('') + ' ' + df['business_scope'].fillna('')
        text = ' '.join(df['text_source'])

        if not text.strip():
             logging.warning(f"社区 {community_id} 的公司缺乏名称和经营范围信息。")
             return 'no_data', None

        # 使用jieba分词并过滤停用词
        word_list = [word for word in jieba.cut(text) if word.strip() and word not in self.stopwords]
        processed_text = ' '.join(word_list)

        if not processed_text:
            logging.warning(f"过滤停用词后，社区 {community_id} 没有可用于生成词云的文本。")
            return 'no_data', None
        
        try:
            wordcloud = WordCloud(
                width=1200, height=600, background_color='white',
                font_path=font_path, collocations=False,
                max_words=200,
                scale=1.5
            ).generate(processed_text)
            
            buffer = BytesIO()
            wordcloud.to_image().save(buffer, format='PNG')
            buffer.seek(0)
            img_str = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            logging.info(f"✅ 社区 {community_id} 词云生成成功。")
            return 'success', img_str

        except Exception as e:
            logging.error(f"为社区 {community_id} 生成词云时发生错误: {e}")
            return 'error', str(e)

    def generate_investment_wordcloud(self, company_name, font_path=FONT_PATH):
        """
        为指定公司的对外投资生成关键词词云 (数据源: 企业名称 + 经营范围)
        :param company_name: 目标公司的确切名称
        :param font_path: 用于显示中文的字体文件路径.
        :return: (status, data)元组. status可以是 'success', 'no_data', 'error'. data是base64图片或错误信息.
        """
        logging.info(f"☁️ 正在为 '{company_name}' 生成对外投资词云...")
        query = """
        MATCH (c:Company {name: $name})-[:HOLDS_SHARE]->(investment:Company)
        RETURN investment.name AS investment_name, investment.经营范围 as business_scope
        """
        df = self._run_query(query, {'name': company_name})
        
        if df.empty:
            logging.warning(f"未找到 '{company_name}' 的任何对外投资信息。")
            return 'no_data', None

        # 结合公司名和经营范围作为词云文本源
        df['text_source'] = df['investment_name'].fillna('') + ' ' + df['business_scope'].fillna('')
        text = ' '.join(df['text_source'])

        if not text.strip():
             logging.warning(f"'{company_name}' 的投资目标缺乏名称和经营范围信息。")
             return 'no_data', None

        # 使用jieba分词并过滤停用词
        word_list = [word for word in jieba.cut(text) if word.strip() and word not in self.stopwords]
        processed_text = ' '.join(word_list)
        
        try:
            wordcloud = WordCloud(
                width=800, height=400, background_color='white',
                font_path=font_path, collocations=False
            ).generate(processed_text)
            
            plt.figure(figsize=(10, 5))
            plt.imshow(wordcloud, interpolation='bilinear')
            plt.axis('off')
            plt.title(f'"{company_name}" 对外投资关键词词云', pad=20)
            
            buffer = BytesIO()
            plt.savefig(buffer, format='png', bbox_inches='tight')
            buffer.seek(0)
            img_str = base64.b64encode(buffer.getvalue()).decode('utf-8')
            plt.close()

            logging.info("✅ 词云生成并编码成功。")
            return 'success', img_str
        except FileNotFoundError:
            err_msg = f"字体文件未找到: {font_path}。请提供正确的字体文件路径以生成中文词云。"
            logging.error(err_msg)
            return 'error', err_msg
        except Exception as e:
            err_msg = f"生成词云时发生错误: {e}"
            logging.error(err_msg)
            return 'error', err_msg

    def find_control_path(self, source_name, target_name, max_depth=10):
        """
        寻找一个股东（人或公司）到目标公司的最短控制路径
        """
        logging.info(f"Pathfinder: 探索从 '{source_name}' 到 '{target_name}' 的控制路径...")
        query = """
        MATCH (source {name: $source_name}), (target:Company {name: $target_name}),
        p = allShortestPaths((source)-[:HOLDS_SHARE*1..{max_depth}]->(target))
        RETURN [node in nodes(p) | node.name] AS path, 
               [rel in relationships(p) | rel.percentage] AS holding_percentages
        """
        params = {'source_name': source_name, 'target_name': target_name, 'max_depth': max_depth}
        df = self._run_query(query, params)
        
        if df.empty:
            logging.warning(f"未找到从 '{source_name}' 到 '{target_name}' 的持股路径。")
        else:
            logging.info("✅ 找到控制路径。")
        return df

    def generate_html_report(self, report_data, filename="股权网络分析报告.html"):
        """
        生成包含所有分析结果的HTML报告
        """
        html_template = f"""
        <html>
        <head>
            <meta charset="UTF-8">
            <title>股权网络分析报告</title>
            <style>
                body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; margin: 40px; background-color: #f8f9fa; color: #333; }}
                .container {{ max-width: 1200px; margin: auto; background: white; padding: 40px; box-shadow: 0 0 15px rgba(0,0,0,0.1); border-radius: 8px; }}
                h1, h2, h3 {{ color: #0056b3; border-bottom: 2px solid #0056b3; padding-bottom: 10px; }}
                h1 {{ text-align: center; }}
                h2 {{ margin-top: 40px; }}
                h3 {{ margin-top: 20px; color: #007bff; border-bottom: 1px solid #007bff; }}
                .table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                .table th, .table td {{ padding: 12px 15px; border: 1px solid #ddd; text-align: left; }}
                .table th {{ background-color: #007bff; color: white; }}
                .table-striped tbody tr:nth-of-type(odd) {{ background-color: #f2f2f2; }}
                .table-hover tbody tr:hover {{ background-color: #e9ecef; }}
                img {{ max-width: 100%; height: auto; display: block; margin: 20px auto; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }}
                .report-info {{ text-align: center; margin-bottom: 40px; color: #666; }}
                .section {{ margin-bottom: 40px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>股权网络分析报告</h1>
                <p class="report-info">报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                
                <div class="section">
                    <h2>整体网络分析</h2>
                    <h3>TOP 10 重要节点 (PageRank)</h3>
                    {self._df_to_html(report_data.get('important_nodes_pr', '无数据'))}
                    <img src="data:image/png;base64,{report_data.get('important_nodes_pr_chart', '')}" alt="PageRank Bar Chart">

                    <h3>TOP 10 重要节点 (度中心性)</h3>
                    {self._df_to_html(report_data.get('important_nodes_degree', '无数据'))}
                    <img src="data:image/png;base64,{report_data.get('important_nodes_degree_chart', '')}" alt="Degree Centrality Bar Chart">
                    
                    <h3>社群规模概览 (Top 10)</h3>
                    {self._df_to_html(report_data.get('community_stats_top10', '无数据'))}
                    <img src="data:image/png;base64,{report_data.get('community_stats_chart', '')}" alt="Community Size Bar Chart">
                </div>

                {''.join(report_data.get('key_community_reports', ['']))}

            </div>
        </body>
        </html>
        """
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_template)
        logging.info(f"✅ HTML报告已生成: {os.path.abspath(filename)}")


    def run_key_community_analysis(self, key_companies, top_n_centrality=10):
        """
        执行针对重点公司的社群分析流程
        :param key_companies: 重点公司名称列表
        :param top_n_centrality: 中心性分析返回的节点数量
        :return: 包含所有分析结果的字典
        """
        report_data = {'key_community_reports': []}
        logging.info("🚀 --- 开始执行重点社群分析流程 --- 🚀")

        # 1. 社区发现 (整个图)
        communities_df = self.find_communities(algorithm='Louvain')
        if communities_df.empty:
            logging.error("社区发现失败，无法继续分析。")
            return report_data

        # 将社区ID写回数据库节点，方便后续查询
        logging.info("正在将社区ID写回Neo4j...")
        query_write_community_id = """
        UNWIND $rows AS row
        MATCH (c:Company {name: row.name})
        SET c.communityId = row.communityId
        """
        self.driver.session().run(query_write_community_id, rows=communities_df.to_dict('records'))
        logging.info("✅ 社区ID已成功写回数据库。")

        # 2. 找到重点公司所在的社区
        key_community_ids = set()
        for company in key_companies:
            if company in communities_df['name'].values:
                community_id = communities_df.loc[communities_df['name'] == company, 'communityId'].iloc[0]
                key_community_ids.add(community_id)
                logging.info(f"找到公司 '{company}' 位于社区 {community_id}")
            else:
                logging.warning(f"在社区分析结果中未找到公司: '{company}'")

        # 3. 对每个重点社区进行深入分析
        for i, cid in enumerate(key_community_ids):
            logging.info(f"--- 正在分析重点社区 {cid} ({i+1}/{len(key_community_ids)}) ---")
            community_report_html = f"<h2>重点社群分析: 社群 {cid}</h2>"
            
            # 3.1 中心性分析
            centrality_df = self.analyze_centrality_for_community(cid, top_n=top_n_centrality)
            if not centrality_df.empty:
                community_report_html += f"""
                <div class="section">
                    <h3>社群内 TOP {top_n_centrality} 核心节点 (PageRank)</h3>
                    {self._df_to_html(centrality_df)}
                </div>
                """

            # 3.2 可视化
            graph_img_b64 = self.visualize_community_graph(cid)
            if graph_img_b64:
                community_report_html += f"""
                <div class="section">
                    <h3>社群网络结构图</h3>
                    <img src="data:image/png;base64,{graph_img_b64}" alt="Community {cid} Graph">
                </div>
                """

            # 3.3 词云分析
            status, wc_img_b64 = self.generate_community_wordcloud(cid)
            if status == 'success' and wc_img_b64:
                community_report_html += f"""
                <div class="section">
                    <h3>社群经营范围词云</h3>
                    <img src="data:image/png;base64,{wc_img_b64}" alt="Community {cid} Word Cloud">
                </div>
                """
            
            report_data['key_community_reports'].append(community_report_html)
        
        # 4. (可选) 清理写回的社区ID
        logging.info("正在清理数据库中的社区ID属性...")
        self._run_query("MATCH (c:Company) WHERE c.communityId IS NOT NULL REMOVE c.communityId")
        logging.info("✅ 已清理社区ID。")
        
        logging.info("✅ --- 重点社群分析流程执行完毕 --- ✅")
        return report_data


def main():
    """
    主执行函数
    """
    analyzer = GraphAnalyzer(NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD)
    report_data = {}

    try:
        # --- 1. 整体网络宏观分析 ---
        logging.info("--- 开始进行整体网络宏观分析 ---")
        # 1.1 重要节点分析
        important_nodes_pr = analyzer.find_important_nodes(top_n=10, algorithm='PageRank')
        report_data['important_nodes_pr'] = important_nodes_pr
        report_data['important_nodes_pr_chart'] = analyzer._generate_horizontal_barchart(
            important_nodes_pr, 'score', 'name', '全网络节点重要性 (PageRank)', 'PageRank Score', '公司/个人'
        )

        important_nodes_degree = analyzer.find_important_nodes(top_n=10, algorithm='Degree')
        report_data['important_nodes_degree'] = important_nodes_degree
        report_data['important_nodes_degree_chart'] = analyzer._generate_horizontal_barchart(
            important_nodes_degree, 'score', 'name', '全网络节点重要性 (度中心性)', 'Degree Score', '公司/个人'
        )

        # 1.2 社区概览分析
        communities_df = analyzer.find_communities(algorithm='Louvain')
        if not communities_df.empty:
            community_stats = analyzer.analyze_community_characteristics(communities_df)
            report_data['community_stats_top10'] = community_stats.head(10)
            report_data['community_stats_chart'] = analyzer._generate_horizontal_barchart(
                community_stats.head(10).sort_values('节点数', ascending=True), '节点数', 'community_label', 'TOP 10 社群规模', '节点数量', '社群'
            )

        # --- 2. 重点社群深度分析 ---
        key_companies = [
            "富华环球投资有限公司",
            "北京翠微大厦股份有限公司",
            "北京翠微集团有限责任公司"
        ]
        key_community_data = analyzer.run_key_community_analysis(key_companies)
        report_data.update(key_community_data)


    except Exception as e:
        logging.error(f"分析过程中发生严重错误: {e}", exc_info=True)
    finally:
        # --- 3. 生成报告 ---
        # 在生成最终报告之前，清理数据库中的临时属性
        logging.info("正在清理数据库中的社区ID属性...")
        analyzer._run_query("MATCH (c:Company) WHERE c.communityId IS NOT NULL REMOVE c.communityId")
        logging.info("✅ 已清理社区ID。")

        analyzer.generate_html_report(report_data)
        analyzer.close()
        logging.info("🎉 分析流程结束。")

if __name__ == '__main__':
    main() 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股权树抓取调度器
- 使用广度优先搜索(BFS)算法遍历公司股权结构
- 管理抓取队列和深度
- 与数据库交互，存储节点和边
"""
import time
import random
from collections import deque
from urllib.parse import urljoin
import re
from datetime import datetime, timedelta

from tianyancha_scraper import TianyanchaCompanyScraper
from graph_manager import GraphManager
import config

class TreeCrawler:
    def __init__(self, use_selenium=False, headless=True):
        self.scraper = TianyanchaCompanyScraper(use_selenium=use_selenium, headless=headless)
        self.db_manager = GraphManager(
            uri=config.NEO4J_URI,
            user=config.NEO4J_USER,
            password=config.NEO4J_PASSWORD
        )

    def _enqueue_nodes(self, nodes, current_depth, queue, visited_urls, relation_type):
        """
        将一组相关节点（股东或投资）加入到抓取队列中。
        
        Args:
            nodes (list): 从数据库或爬虫获取的节点信息列表。
            current_depth (int): 当前抓取深度。
            queue (deque): 抓取队列。
            visited_urls (set): 已访问URL集合。
            relation_type (str): 关系类型描述，用于打印日志（例如: '股东' 或 '投资'）。
        """
        new_nodes_found = 0
        # 统一 '股东链接' 和 '被投公司链接'
        url_key = 'url' if 'url' in nodes[0] else ('股东链接' if '股东链接' in nodes[0] else '被投公司链接')
        name_key = 'name' if 'name' in nodes[0] else ('股东名称' if '股东名称' in nodes[0] else '被投公司名称')
        
        for node in nodes:
            node_url = node.get(url_key)
            if node_url and 'company' in node_url:
                # 确保URL是完整的
                full_url = urljoin('https://www.tianyancha.com', node_url)
                if full_url not in visited_urls:
                    visited_urls.add(full_url)
                    queue.append((full_url, current_depth + 1))
                    new_nodes_found += 1
                    print(f"  └── [{relation_type}] 发现新公司，加入队列: {node.get(name_key)}")
        
        if new_nodes_found == 0:
            print(f"  └── [{relation_type}] 未发现新的上层或下层公司。")

    def crawl(self, start_urls, max_depth=3, direction='up', force_refresh=False):
        """
        开始执行股权树的抓取任务
        Args:
            start_urls (list): 起始公司URL列表
            max_depth (int): 最大追溯的层数
            direction (str): 抓取方向: 'up', 'down', 'both'
            force_refresh (bool): 是否强制刷新，忽略缓存
        """
        queue = deque([(url, 0) for url in start_urls])
        visited_urls = set(start_urls)

        print(f"🚀 === 开始股权穿透任务 ===")
        print(f"🌱 起始节点数: {len(start_urls)}")
        print(f"🔝 最大深度: {max_depth}")
        print(f"🧭 抓取模式: {direction.upper()}")
        if force_refresh:
            print("🔥 强制刷新模式已开启，将忽略所有缓存！")
        print(f"==========================")

        try:
            while queue:
                current_url, current_depth = queue.popleft()

                print(f"\n" + "-"*70)
                print(f"➡️ 正在处理 Level-{current_depth}: {current_url}")
                
                # 检查节点是否最近更新过 (仅在非强制模式下)
                if not force_refresh:
                    t_id_match = re.search(r'company/(\d+)', current_url)
                    if t_id_match:
                        node_t_id = int(t_id_match.group(1))
                        is_recent, shareholders_from_db, investments_from_db = self.db_manager.get_relations_from_db(node_t_id)
                        
                        if is_recent:
                            print(f"✅ 节点最近已更新，跳过抓取，直接从数据库加载关系。")
                            
                            if direction in ['up', 'both'] and shareholders_from_db:
                                self._enqueue_nodes(shareholders_from_db, current_depth, queue, visited_urls, "从DB加载股东")
                            
                            if direction in ['down', 'both'] and investments_from_db:
                                self._enqueue_nodes(investments_from_db, current_depth, queue, visited_urls, "从DB加载投资")
                            
                            continue # 跳到下一个循环
                
                if current_depth >= max_depth:
                    print(f"⛔️ 已达到最大深度 {max_depth}，不再追溯。")
                    continue

                # 1. 抓取当前公司的详细信息 (传入direction)
                scraped_data = self.scraper.scrape_company_info(current_url, direction=direction)
                if not scraped_data or not scraped_data.get('basic_info'):
                    print(f"⚠️ 未能抓取到有效的基本信息，跳过: {current_url}")
                    continue

                # 2. 将抓取的数据存入图数据库 (使用新的标准化方法)
                self.db_manager.save_company_data(scraped_data)
                
                # 3. 如果需要，处理上行和下行关系
                if current_depth < max_depth:
                    if direction in ['up', 'both']:
                        shareholders = scraped_data.get('shareholders_info', [])
                        if shareholders:
                            self._enqueue_nodes(shareholders, current_depth, queue, visited_urls, "向上追溯股东")

                    if direction in ['down', 'both']:
                        investments = scraped_data.get('investments_info', [])
                        if investments:
                            self._enqueue_nodes(investments, current_depth, queue, visited_urls, "向下追溯投资")

                # 在两次请求之间随机暂停
                sleep_time = random.uniform(3, 10)
                print(f"⏳ 暂停 {sleep_time:.2f} 秒...")
                time.sleep(sleep_time)

        except KeyboardInterrupt:
            print("\n🛑 用户手动中断程序。")
        except Exception as e:
            print(f"\n💥 抓取过程中发生严重错误: {e}")
        finally:
            self.scraper.close()
            self.db_manager.close()
            print("\n🏁 === 股权穿透任务结束 ===")

    def search_and_start(self, company_names, max_depth=3, direction='up', force_refresh=False):
        """
        根据公司名称搜索，并从第一个搜索结果开始抓取
        Args:
            company_names (list): 公司名称列表
            max_depth (int): 最大深度
            direction (str): 抓取方向: 'up', 'down', 'both'
            force_refresh (bool): 是否强制刷新
        """
        start_urls = []
        for name in company_names:
            search_result = self.scraper.search_and_get_first_company(name)
            if search_result and search_result.get('url'):
                start_urls.append(search_result['url'])
            else:
                print(f"❌ 未能找到公司 '{name}' 的起始URL，已跳过。")
        
        if start_urls:
            self.crawl(start_urls, max_depth, direction, force_refresh)
        else:
            print("❌ 未找到任何有效的起始公司URL，任务无法开始。")

if __name__ == '__main__':
    # --- 使用示例 ---
    
    # 1. 定义起始公司
    # 可以是公司名称，脚本会自动搜索
    initial_companies = ["小米科技有限责任公司"] 
    
    # 也可以直接提供URL
    # initial_urls = ["https://www.tianyancha.com/company/15964459"] 

    # 2. 设置向上追溯的深度
    penetration_depth = 4

    # 3. 创建并运行爬虫
    # use_selenium=True 可以获取更完整的动态加载数据，但速度较慢
    tree_crawler = TreeCrawler(use_selenium=True)
    
    # 如果使用公司名称启动
    # tree_crawler.search_and_start(initial_companies, max_depth=penetration_depth) # 默认 up
    # tree_crawler.search_and_start(initial_companies, max_depth=penetration_depth, direction='down')
    tree_crawler.search_and_start(initial_companies, max_depth=penetration_depth, direction='both', force_refresh=True)

    # 如果直接使用URL启动
    # tree_crawler.crawl(initial_urls, max_depth=penetration_depth) 